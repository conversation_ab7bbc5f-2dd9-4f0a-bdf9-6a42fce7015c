<template>
  <div class="infoWindow main">
    <table class="table" border="1" bordercolor="#eee">
        <colgroup>
            <col width="100">
            <col width="220">
            <col width="100">
            <col width="210">
        </colgroup>
        <thead></thead>
        <tbody>
        <tr>
            <td>车辆号牌</td>
            <td>{{ info.vehicleId }}</td>

            <td>所属企业</td>
            <td>{{ info.carTeamName }}</td>
        </tr>
        <tr>
            <td>定位时间</td>
            <td colspan="3">{{ info.posTime }}</td>
        </tr>
        <tr>
            <td>经度</td>
            <td>{{ info.longitude }}</td>
            <td>纬度</td>
            <td>{{ info.latitude }}</td>
        </tr>
        </tbody>
    </table>
  </div>
</template>

<script>
  export default {
    data() {
        return {
        };
    },
    // 传入参数
    props: {
        info: Object
    },
    methods: {
    }
  };
</script>
<style lang="scss" scoped>
  .infoWindow {
    background: #fff;
    padding: 10px !important;
    border-radius: 5px;
    //box-shadow: 0 0 10px rgba(0, 0, 0, .3);
    width: 600px;
    z-index: 100;
    min-width: 400px !important;

    //鼠标不显示
    -moz-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;

    table {
      text-align: center;
      font-size: 12px;
    }

    .el-table td {
      padding: 10px 0;
    }

    .table {
      width: 100%;
      margin: 10px 0;
      background-color: #fff;
      color: #5f5f5f;
      border-collapse: collapse;
    }

    .table td,
    .table th {
      position: relative;
      padding: 10px 15px;
      min-height: 20px;
      line-height: 20px;
      font-size: 14px;
      word-break: break-all;
    }
    .table tr {
      &:hover {
        background-color: #f9f9f9;
      }
    }

    .el-table th.el-table__cell > .cell {
      color: #333;
    }

  }
</style>
