import {merge} from "lodash";

export class gaodeUtil {

  /**
   * AMapLoader.load option
   * @returns
   */
  static loadOption(opt) {
    // AMapLoader.reset();
    const defaultOpt = {
      key: process.env.VUE_APP_MAP_KEY,  //设置您的key
      version: "2.0",
      plugins: ['AMap.ToolBar'],
      AMapUI: {
        version: "1.1", plugins: [],
      }, Loca: {
        version: "2.0"
      },
    };
    //自定义值替换默认值
    opt = merge({}, defaultOpt, opt);
    return opt;
  }

  /**
   * map初始化选项
   * @returns
   */
  static initOption(opt) {
    const defaultOpt = {
      viewMode: "2D", zoom: 12, center: this.getCenter(),
    };
    //自定义值替换默认值
    opt = merge({}, defaultOpt, opt);
    return opt;
  }

  /**
   * 从配置文件获取地图中心点
   * @returns {*}
   */
  static getCenter() {
    // let mapCenter = process.env.VUE_APP_MAP_CENTER;
    return null;
  }

  /**
   * 创建多边形
   * @param mapObj            map对象
   * @param path              路径
   * @returns {AMap.Polygon}  {*|o.Polygon|r.Polygon}
   */
  static createPolygon(mapObj, path) {
    let arr = path;
    return new AMap.Polygon({
      map: mapObj,
      path: arr,
      strokeColor: "#0000ff",
      strokeOpacity: 1,
      strokeWeight: 3,
      fillColor: "#f5deb3",
      fillOpacity: 0.35
    });
  }

  /**
   * 创建point对象 { lng:lng , lat : lat}
   */
  static createPoint(opt) {
    return new AMap.LngLat(opt.lng, opt.lat);
  }

  /**
   * 创建ICON对象  { width:36 , height:36 , image: 图片路径 }
   * @param opt
   * @returns {Icon}
   */
  static createIcon(opt) {
    let defaultOpt = {
      size: new AMap.Size(opt.width || 36, opt.height || 36), image: opt.image
    };
    opt = merge({}, defaultOpt, opt);
    return new AMap.Icon(opt);
  }

  /**
   * 创建一般标记
   * opt {lng:lng , lat:lat}
   */
  static createMarker(mapObj, opt) {
    opt = opt || {};
    let defaultOpt = {
      map: mapObj, position: this.createPoint(opt), angle: 0, title: ''
    };
    if (opt.image) {
      opt.icon = this.createIcon(opt);
      try {
        let size = opt.icon.getSize();
        defaultOpt.offset = new AMap.Pixel(-(parseInt(size[0]) / 2), -parseInt(size[1]));
      } catch (e) {
        let size = opt.icon;
        defaultOpt.offset = new AMap.Pixel(-(parseInt(size.get('width'))/2) , -parseInt(size.get('height')));
      }
    }
    opt = merge({}, defaultOpt, opt);
    let marker = new AMap.Marker(opt);
    marker.setAngle(opt.angle);
    return marker;
  }

  /**
   * 创建自定义标记
   * eg: opt = {lng: longitude, lat:latitude,image:'red.png',width:52,height:52}
   * lng lat 经纬度
   * 可选参数(path /assets/images 目录下的 图片名称， width 图片宽度 ，图片高度)
   *
   * @param opt
   */
  static createCustomMarker(mapObj, opt) {
    let defaultOpt = {
      width: 32, height: 32, angle: 0
    };
    opt = merge({}, defaultOpt, opt);
    return this.createMarker(mapObj, opt);
  }

  /**
   * 轨迹展示(不带巡航功能)
   * @param opt
   * data : 轨迹线数据
   * strokeStyle ： 轨迹线颜色
   */
  static showTrace(mapObj, opt) {
    let defaultOpt = {
      zIndex: 100, path: [], strokeColor: '#28F', strokeWeight: 6,showDir: true
    };
    opt = merge({}, defaultOpt, opt);
    opt.map = mapObj;
    // 绘制轨迹
    new AMap.Polyline(opt);
    let startPot = opt.path[0];
    let endPot = opt.path[opt.path.length - 1];
    new AMap.Marker({
      map: mapObj, position: [startPot[0], startPot[1]], //基点位置
      icon: "https://webapi.amap.com/theme/v1.3/markers/n/start.png", zIndex: opt.zIndex,
      offset: new AMap.Pixel(-10, -30)
    });
    new AMap.Marker({
      map: mapObj, position: [endPot[0], endPot[1]], //基点位置
      icon: "https://webapi.amap.com/theme/v1.3/markers/n/end.png", zIndex: opt.zIndex,
      offset: new AMap.Pixel(-10, -30)
    });
    mapObj.setFitView();
  }

  /**
   * 轨迹展示(带巡航功能)
   * @param opt
   * data : 轨迹线数据
   * strokeStyle ： 轨迹线颜色
   */
  static createPathSimplifier(mapObj, opt) {
    let defaultOpt = {
      zIndex: 100, data: [], strokeStyle: 'red', lineWidth: 6, dirArrowStyle: true
    };
    opt = merge({}, defaultOpt, opt);
    if (window.pathSimplifierIns) {
      //通过该方法清空上次传入的轨迹
      window.pathSimplifierIns.setData([]);
      //清楚地图上的覆盖物
      mapObj.clearMap();
    }
    AMapUI.load(['ui/misc/PathSimplifier', 'lib/$'], (PathSimplifier, $) => {
      if (!PathSimplifier.supportCanvas) {
        alert('当前环境不支持 Canvas！');
      }
      window.pathSimplifierIns = new PathSimplifier({
        zIndex: opt.zIndex,
        autoSetFitView: true,
        map: mapObj, //所属的地图实例
        getPath: (pathData, pathIndex) => {
          return pathData.path;
        }, getHoverTitle: (pathData, pathIndex, pointIndex) => {
          //返回鼠标悬停时显示的信息
          if (pointIndex >= 0) {
            //鼠标悬停在某个轨迹节点上
            return pathData.name + '，点:' + pointIndex + '/' + pathData.path.length;
          }
          //鼠标悬停在节点之间的连线上
          return '车牌号:' + pathData.name + '，点数量:' + pathData.path.length;
        }, renderOptions: {
          //轨迹线的样式
          pathLineStyle: {
            strokeStyle: opt.strokeStyle,
            lineWidth: opt.lineWidth,
            dirArrowStyle: opt.dirArrowStyle
          },
          //鼠标hover时的title信息
          hoverTitleStyle: {
            position: 'top'
          }
        }
      });
      /**创建轨迹方法*/
      window.pathSimplifierIns.setData(opt.data);

      /**==========画起点和终点marker start*/
      let startPot = opt.data[0].path[0];
      let endPot = opt.data[0].path[opt.data[0].path.length - 1];
      new AMap.Marker({
        map: mapObj, position: [startPot[0], startPot[1]], //基点位置
        icon: "https://webapi.amap.com/theme/v1.3/markers/n/start.png", zIndex: opt.zIndex,
        offset: new AMap.Pixel(-10, -30)
      });
      new AMap.Marker({
        map: mapObj, position: [endPot[0], endPot[1]], //基点位置
        icon: "https://webapi.amap.com/theme/v1.3/markers/n/end.png", zIndex: opt.zIndex,
        offset: new AMap.Pixel(-10, -30)
      });
      /**==========画起点和终点marker end*/
      mapObj.setFitView();
      window.navg = window.pathSimplifierIns.createPathNavigator(0, {
        loop: false, // 循环播放
        speed: 100, // 巡航速度，单位千米/小时
        pathNavigatorStyle: {
          width: 16,
          height: 32,
          content: PathSimplifier.Render.Canvas.getImageContent(require("@/assets/images/3-car.png"), onload, onerror),
          strokeStyle: null,
          fillStyle: null
        }
      })
    });
  }

  static createInfoWindow(opt) {
    let defaultOpt = {
      isCustom: false,
      autoMove: true,
      avoid: [20, 20, 20, 20],
      content: "",
      closeWhenClickMap: true,
      offset: new AMap.Pixel(-300, -180)
    };
    opt = merge({}, defaultOpt, opt);
    return new AMap.InfoWindow(opt);
  }

  static createVideoInfoWindow(opt) {
    let defaultOpt = {
      isCustom: false,
      autoMove: true,
      avoid: [20, 20, 20, 20],
      content: "",
      closeWhenClickMap: true,
      offset: new AMap.Pixel(-300, -180)
    };
    opt = merge({}, defaultOpt, opt);
    return new AMap.InfoWindow(opt);
  }
}
