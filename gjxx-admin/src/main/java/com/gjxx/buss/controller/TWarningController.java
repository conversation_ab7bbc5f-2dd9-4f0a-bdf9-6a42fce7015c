package com.gjxx.buss.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.gjxx.common.core.domain.R;
import com.gjxx.common.utils.old.ConvertUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.gjxx.common.annotation.Log;
import com.gjxx.common.core.controller.BaseController;
import com.gjxx.common.enums.BusinessType;
import com.gjxx.buss.domain.TWarning;
import com.gjxx.buss.service.ITWarningService;
import com.gjxx.common.utils.poi.ExcelUtil;
import com.gjxx.common.core.page.TableDataInfo;

/**
 * 报警信息Controller
 *
 * <AUTHOR>
 * @date 2024-07-09
 */
@RestController
@RequestMapping("/buss/warning")
public class TWarningController extends BaseController {
    @Autowired
    private ITWarningService tWarningService;

    /**
     * 查询报警信息列表
     */
    @PreAuthorize("@ss.hasPermi('buss:warning:list')")
    @GetMapping("/list")
    public TableDataInfo list(TWarning tWarning) {
        startPage();
        List<TWarning> list = tWarningService.selectTWarningList(tWarning);
        return getDataTable(list);
    }

    /**
     * 导出报警信息列表
     */
    @PreAuthorize("@ss.hasPermi('buss:warning:export')")
    @Log(title = "报警信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TWarning tWarning) {
        ExcelUtil<TWarning> util = new ExcelUtil<>(TWarning.class);

        List<TWarning> list = tWarningService.selectTWarningList(tWarning);
        util.exportExcel(response, list, "报警信息数据");
    }

    /**
     * 获取报警信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('buss:warning:query')")
    @GetMapping(value = "/{id}")
    public R<TWarning> getInfo(@PathVariable("id") Long id) {
        return R.ok(tWarningService.getById(id));
    }

    /**
     * 新增报警信息
     */
    @PreAuthorize("@ss.hasPermi('buss:warning:add')")
    @Log(title = "报警信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody TWarning tWarning) {
        return R.ok(tWarningService.save(tWarning));
    }

    /**
     * 修改报警信息
     */
    @PreAuthorize("@ss.hasPermi('buss:warning:edit')")
    @Log(title = "报警信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody TWarning tWarning) {
        return R.ok(tWarningService.updateById(tWarning));
    }

    /**
     * 删除报警信息
     */
    @PreAuthorize("@ss.hasPermi('buss:warning:remove')")
    @Log(title = "报警信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        return R.ok(tWarningService.removeBatchByIds(CollectionUtils.arrayToList(ids)));
    }

    /**
     * 批量处理报警信息
     */
    @PreAuthorize("@ss.hasPermi('buss:warning:handleBatch')")
    @Log(title = "批量处理报警信息", businessType = BusinessType.UPDATE)
    @GetMapping("/handleBatch/{ids}")
    public R<Integer> handleBatch(@PathVariable Long[] ids) {
        String userName = getUsername();
        return R.ok(tWarningService.handleBatchByIds(ids,userName));
    }

    /**
     * 暂不处理报警信息
     */
    @PreAuthorize("@ss.hasPermi('buss:warning:handleNo')")
    @Log(title = "暂不处理报警信息", businessType = BusinessType.UPDATE)
    @GetMapping("/handleNo/{ids}")
    public R<Integer> handleNo(@PathVariable Long[] ids) {
        String userName = getUsername();
        return R.ok(tWarningService.handleNoByIds(ids,userName));
    }
}
