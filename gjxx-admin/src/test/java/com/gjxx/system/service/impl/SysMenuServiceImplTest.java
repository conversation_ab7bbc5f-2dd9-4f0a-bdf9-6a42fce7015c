package com.gjxx.system.service.impl;

import com.gjxx.BaseServiceTest;
import com.gjxx.common.core.domain.entity.SysMenu;
import com.gjxx.system.service.ISysMenuService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SysMenuServiceImplTest extends BaseServiceTest {

    @Autowired
    private ISysMenuService menuService;

    @Test
    void getById() {
        SysMenu byId = this.menuService.getById(1L);
        System.out.println("byId = " + byId);
    }

    @Test
    void selectMenuById() {
        SysMenu byId = this.menuService.selectMenuById(1L);
        System.out.println("byId = " + byId);
    }
}
