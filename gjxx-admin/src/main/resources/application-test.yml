# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8100
  port: 8100
  servlet:
    # 应用的访问路径
    context-path: /

publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJtBEwWHn5JW0UjzYtVFeYwTX7aRI7cSmOkXQNMa261rZbRbhZEpn/3CPwhHSFzPaPczU+V+6LpfjHSBhnIS7TUCAwEAAQ==
# Spring配置
spring:
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 9601
    # 数据库索引
    database: 7
    # 密码
    password: Gjxx#123456
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: **************************************************************************************************************************************************
        username: root
        password: U0GJSce2nbQk2F3i939TaAAT53WWPHwWBaHk/2iMywermVBaW9g7gS6NzJK7YdFTCAh9uLAyDJrVCJ88G1sFJw==
        connection-properties: config.decrypt=true;config.decrypt.key=${publicKey}
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /test-api

# 视频播放相关配置
video:
  # 视频服务器地址
  sinkIP: 127.0.0.1
  # 视频服务器端口
  sinkPort: 11935
  # 视频播放端口
  sinkPlayPort: 17789
  # 视频播放IP地址
  sinkPlayIP: 127.0.0.1
  # 视频播放第二个端口
  sinkPlayTwoPort: 9501
  # 视频播放第三个端口
  sinkPlayThreePort: 9502
  # 视频播放第四个端口
  sinkPlayFourPort: 9506
  # 视频播放第五个端口
  sinkPlayFivePort: 9507
