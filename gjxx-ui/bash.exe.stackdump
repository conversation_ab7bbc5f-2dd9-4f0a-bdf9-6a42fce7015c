Stack trace:
Frame         Function      Args
0007FFFF95D0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF95D0, 0007FFFF84D0) msys-2.0.dll+0x1FE8E
0007FFFF95D0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF98A8) msys-2.0.dll+0x67F9
0007FFFF95D0  000210046832 (000210286019, 0007FFFF9488, 0007FFFF95D0, 000000000000) msys-2.0.dll+0x6832
0007FFFF95D0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF95D0  000210068E24 (0007FFFF95E0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF98B0  00021006A225 (0007FFFF95E0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9B27B0000 ntdll.dll
7FF9B0BD0000 KERNEL32.DLL
7FF9AFDE0000 KERNELBASE.dll
7FF9B0450000 ADVAPI32.DLL
7FF9B1D20000 msvcrt.dll
7FF9B0510000 sechost.dll
7FF9B0420000 bcrypt.dll
7FF9B12E0000 RPCRT4.dll
7FF9ABB20000 apphelp.dll
7FF9B0CA0000 USER32.dll
7FF9AFC90000 win32u.dll
7FF9B0670000 GDI32.dll
7FF9B0300000 gdi32full.dll
7FF9AFA00000 msvcp_win.dll
7FF9AFCC0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9AEFC0000 CRYPTBASE.DLL
7FF9AF980000 bcryptPrimitives.dll
7FF9B0860000 IMM32.DLL
