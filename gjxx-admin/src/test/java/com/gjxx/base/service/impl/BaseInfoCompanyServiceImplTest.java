package com.gjxx.base.service.impl;

//import com.gjxx.base.domain.BaseInfoCompany;
import com.gjxx.BaseServiceTest;
//import com.gjxx.base.service.IBaseInfoCompanyService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class BaseInfoCompanyServiceImplTest extends BaseServiceTest {

//    @Autowired
//    private IBaseInfoCompanyService baseInfoCompanyService;

    @Test
    void selectBaseInfoCompanyById() {
//        BaseInfoCompany baseInfoCompany = this.baseInfoCompanyService.getById(2l);
//        System.out.println("baseInfoCompany = " + baseInfoCompany);
    }

    @Test
    void selectBaseInfoCompanyList() {
    }

    @Test
    void insertBaseInfoCompany() {
    }

    @Test
    void updateBaseInfoCompany() {
    }

    @Test
    void deleteBaseInfoCompanyByIds() {
    }

    @Test
    void deleteBaseInfoCompanyById() {
    }
}
