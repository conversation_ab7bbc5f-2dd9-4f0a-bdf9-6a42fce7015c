package com.gjxx.common.core.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * Tree基类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MpTreeEntity extends MpBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 父菜单名称
     */
    @JsonIgnore
    @TableField(exist = false)
    private String parentName;

    /**
     * 父菜单ID
     */
    @JsonIgnore
    @TableField(exist = false)
    private Long parentId;

    /**
     * 显示顺序
     */
    @JsonIgnore
    @TableField(exist = false)
    private Integer orderNum;

    /**
     * 祖级列表
     */
    @JsonIgnore
    @TableField(exist = false)
    private String ancestors;

    /**
     * 子部门
     */
    @JsonIgnore
    @TableField(exist = false)
    private List<?> children = new ArrayList<>();

}
