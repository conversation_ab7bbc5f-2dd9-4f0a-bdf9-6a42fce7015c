package com.gjxx.buss.controller;

import com.alibaba.fastjson2.JSON;
import com.gjxx.buss.domain.TCarDev;
import com.gjxx.buss.domain.TCarTeam;
import com.gjxx.buss.domain.bean.CarDevLocationAndVedioPlayBean;
import com.gjxx.buss.service.ITCarDevService;
import com.gjxx.buss.service.ITCarTeamService;
import com.gjxx.common.core.controller.BaseController;
import com.gjxx.common.core.domain.R;
import com.gjxx.common.core.redis.RedisCache;
import com.gjxx.common.tcp.LocationInfoQueryMsg;
import com.gjxx.common.utils.DateUtils;
import com.gjxx.mongo.entity.CarDevOnlineStatus;
import com.gjxx.mongo.service.CarDevOnlineStatusService;
import com.gjxx.netty.codec.TPMSConsts;
import com.gjxx.until.GaoDeMapUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 上报信息缓存查询
 */
@Slf4j
@RestController
@RequestMapping("/buss/msgUpSelect")
public class MsgUpSelectController extends BaseController {
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ITCarDevService tCarDevService;
    @Autowired
    private CarDevOnlineStatusService carDevOnlineStatusService;
    @Autowired
    private ITCarTeamService tCarTeamService;

    /**
     * 查询车辆实时位置
     */
    @PreAuthorize("@ss.hasPermi('buss:msgUpSelect:locationQuery')")
    @GetMapping("/locationQuery")
    public R<CarDevLocationAndVedioPlayBean> locationQuery(Long carDevId,String flowId) {
        CarDevLocationAndVedioPlayBean carDevLocationAndVedioPlayBean = new CarDevLocationAndVedioPlayBean();
        if(!redisCache.hasKey(TPMSConsts.cmd_location_query+flowId)){
            return R.ok(null);
        }
        String  msgStr = redisCache.getCacheObject(TPMSConsts.cmd_location_query+flowId);
        LocationInfoQueryMsg locationInfoQueryMsg = JSON.parseObject(msgStr, LocationInfoQueryMsg.class);
        TCarDev tCarDev = tCarDevService.getById(carDevId);
        CarDevOnlineStatus carDevOnlineStatus = carDevOnlineStatusService.findCarDevOnlineStatusById(tCarDev.getSimNo());
        StringBuilder buffer = new StringBuilder();
        buffer.append("在线,");
        if("01".equals(carDevOnlineStatus.getAccStatus())){
            buffer.append("点火,");
        }else {
            buffer.append("未点火,");
        }
        if("01".equals(carDevOnlineStatus.getWarningStatus())){
            buffer.append("报警");
        }else {
            buffer.append("未报警");
        }
        carDevLocationAndVedioPlayBean.setCarDevStatus(buffer.toString());
        carDevLocationAndVedioPlayBean.setVehicleId(tCarDev.getVehicleId());
        carDevLocationAndVedioPlayBean.setVehicleNoColor(tCarDev.getVehicleNoColor());
        carDevLocationAndVedioPlayBean.setSimNo(tCarDev.getSimNo());
        TCarTeam tCarTeam = tCarTeamService.getById(tCarDev.getCarTeamId());
        if(tCarTeam != null){
            carDevLocationAndVedioPlayBean.setCarTeamName(tCarTeam.getName());
        }
        BigDecimal d_longitude = new BigDecimal(locationInfoQueryMsg.getLongitude());
        carDevLocationAndVedioPlayBean.setLongitude(d_longitude.divide(new BigDecimal(1000000)).toString());
        BigDecimal d_latitude = new BigDecimal(locationInfoQueryMsg.getLatitude());
        carDevLocationAndVedioPlayBean.setLatitude(d_latitude.divide(new BigDecimal(1000000)).toString());
        try {
            Date date = DateUtils.parseDate(locationInfoQueryMsg.getTrajectoryTime(), DateUtils.yyMMddHHmmss);
            carDevLocationAndVedioPlayBean.setPosTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,date));
        }catch (Exception e){
            e.printStackTrace();
            log.error("查询车辆实时位置定位时间转换失败！");
        }
        String locationAddress = "";
        try {
            locationAddress = GaoDeMapUtil.getLocationFromCoordinates(carDevLocationAndVedioPlayBean.getLongitude(),carDevLocationAndVedioPlayBean.getLatitude());
        }catch (Exception e){
            e.printStackTrace();
            log.error("经纬度转换位置信息失败");
        }
        carDevLocationAndVedioPlayBean.setLocationAddress(locationAddress);
        return R.ok(carDevLocationAndVedioPlayBean);
    }
}
