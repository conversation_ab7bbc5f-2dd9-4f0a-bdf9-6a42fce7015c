package com.gjxx.buss.domain.bean;

import com.gjxx.common.tcp.LocationInfoQueryMsg;
import lombok.Data;

import java.io.Serializable;

/**
 * @ Description：RTMP协议调用视频服务器接口参数对象
 */
@Data
public class VideoRtmpParamReturn implements Serializable{
	/**
	 * 流媒体转发服务地址
	 */
	private String rtmpSvrIP;
	/**
	 * 流媒体转发服务RTMP通信端口
	 */
	private String rtmpSvrPort;
	/**
	 * 接入服务器地址
	 */
	private String pagIP;
	/**
	 * 接入服务器Http通信端口
	 */
	private  String pagPort;
	/**
	 * 播放URL
	 */
	private  String playUrl;
	/**
	 * 接入服务器Https地址
	 */
	private  String httpsIp;
	/**
	 * 车牌标识  应采用UTF-8编码，并统一转换为IETF RFC 2854中application/x-www-form-URLencoded MIME格式
	 */
	private String vehicleId;
	/**
	 * 车辆颜色
	 */
	private String vechileColor;
	/**
	 * 终端手机号，固定为12位，手机号不足12位前面补0
	 */
	private String mobileNumber;
	/**
	 * 逻辑通道号
	 */
	private String logicalChn;
	/**
	 * 音视频标志
	 */
	private String dataType;
	/**
	 * 码流类型
	 */
	private String streamType;
	/**
	 * 存储位置
	 */
	private String storageType;
	/**
	 * 回放模式
	 */
	private String replayType;
	/**
	 * 快进或关键帧快退倍数
	 */
	private String multiples;
	/**
	 * 开始时间
	 */
	private String beginTime;
	/**
	 * 结束时间
	 */
	private String endTime;
	/**
	 * 当前终端所属线路，从1开始
	 */
	private String netZone;
	/**
	 * 取流校验口令，暂时保留，长度为64个ASII字符，每24h更新一次
	 */
	private String token;

	/**
	 * 位置信息查询返回
	 */
	private LocationInfoQueryMsg locationInfoQueryMsg;
}
