package com.gjxx.buss.service.impl;

import com.gjxx.common.mp.service.impl.MpServiceImpl;
import java.util.List;
import org.springframework.stereotype.Service;
import com.gjxx.buss.mapper.TWarningMapper;
import com.gjxx.buss.domain.TWarning;
import com.gjxx.buss.service.ITWarningService;

/**
 * 报警信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-09
 */
@Service
public class TWarningServiceImpl extends MpServiceImpl<TWarningMapper, TWarning> implements ITWarningService {

    /**
     * 查询报警信息列表
     *
     * @param tWarning 报警信息
     * @return 报警信息
     */
    @Override
    public List<TWarning> selectTWarningList(TWarning tWarning) {
        return baseMapper.selectTWarningList(tWarning);
    }

    @Override
    public int handleBatchByIds(Long[] ids,String userName) {
        return baseMapper.handleBatchByIds(ids,userName);
    }

    @Override
    public int handleNoByIds(Long[] ids,String userName) {
        return baseMapper.handleNoByIds(ids,userName);
    }

}
