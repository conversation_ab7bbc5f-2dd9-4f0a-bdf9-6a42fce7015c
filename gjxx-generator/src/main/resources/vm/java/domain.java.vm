package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import com.baomidou.mybatisplus.annotation.*;
import com.gjxx.common.annotation.Excel;
#if($table.crud || $table.sub)
#elseif($table.tree)
#end
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gjxx.common.core.domain.MpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
#set($Entity="MpBaseEntity")
#elseif($table.tree)
#set($Entity="TreeEntity")
#end
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName("$tableName")
@ApiModel(value = "${functionName}")
public class ${ClassName} extends ${Entity}{
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
    /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($parentheseIndex != -1)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
#elseif($column.javaType == 'Date' && $column.columnType == 'date')
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
#elseif($column.javaType == 'Date' && $column.columnType != 'date')
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
#else
    @Excel(name = "${comment}")
#end
#end
#if($column.isRequired == 1)
    @ApiModelProperty(value = "${column.columnComment}",required = true)
#else
    @ApiModelProperty("${column.columnComment}")
#end
#if($column.isPk == 1)
#if($column.isIncrement == 1)
    @TableId(value = "id",type = IdType.AUTO)
#else
    @TableId("$column.columnName")
#if($column.javaType == 'Long')
    @JsonSerialize(using = ToStringSerializer.class)
#end
#end
#elseif($column.columnName == 'create_time')
    @TableField(value = "$column.columnName", fill = FieldFill.INSERT)
#elseif($column.columnName == 'update_time')
    @TableField(value = "$column.columnName", fill = FieldFill.INSERT_UPDATE)
#elseif($column.columnName == 'create_by')
    @TableField(value = "create_by", fill = FieldFill.INSERT)
#elseif($column.columnName == 'update_by')
    @TableField(value = "update_by", fill = FieldFill.UPDATE)
#end
    private $column.javaType $column.javaField;

#end
#if($table.sub)
    /** $table.subTable.functionName信息 */
    private List<${subClassName}> ${subclassName}List;

#end
#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
#if($column.javaField.length() > 2 && $column.javaField.substring(1,2).matches("[A-Z]"))
#set($AttrName=$column.javaField)
#else
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
#end
##    public void set${AttrName}($column.javaType $column.javaField)
##    {
##        this.$column.javaField = $column.javaField;
##    }
##
##    public $column.javaType get${AttrName}()
##    {
##        return $column.javaField;
##    }
#end
#end

#if($table.sub)
##    public List<${subClassName}> get${subClassName}List()
##    {
##        return ${subclassName}List;
##    }
##
##    public void set${subClassName}List(List<${subClassName}> ${subclassName}List)
##    {
##        this.${subclassName}List = ${subclassName}List;
##    }

#end
}
