package com.gjxx.buss.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.gjxx.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gjxx.common.core.domain.MpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 报警信息对象 t_warning
 *
 * <AUTHOR>
 * @date 2024-07-09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName("t_warning")
@ApiModel(value = "报警信息")
public class TWarning extends MpBaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty("ID")
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** SIM卡号 */
    @Excel(name = "SIM卡号")
    @ApiModelProperty("SIM卡号")
    private String simNo;

    /** 报警类型 */
    @Excel(name = "报警类型")
    @ApiModelProperty("报警类型")
    private String warningType;

    /** 报警次数 */
    @Excel(name = "报警次数")
    @ApiModelProperty("报警次数")
    private Long warningCount;

    /** 处理状态 */
    @Excel(name = "处理状态")
    @ApiModelProperty("处理状态")
    private String handleStatus;

    /** 处理人 */
    @Excel(name = "处理人")
    @ApiModelProperty("处理人")
    private String handleUser;

    /** 处理时间 */
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("处理时间")
    private Date handleTime;

    /** 创建时间 */
    @ApiModelProperty("第一次报警时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty("最后一次报警时间")
    private Date updateTime;

    /** 车辆标识 */
    @TableField(exist = false)
    private String vehicleId;
}
