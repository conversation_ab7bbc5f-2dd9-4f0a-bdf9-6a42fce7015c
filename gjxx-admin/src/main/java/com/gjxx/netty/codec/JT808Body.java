package com.gjxx.netty.codec;

import com.gjxx.buss.domain.bean.VideoParamBean;
import com.gjxx.netty.req.VideoPlayParam;

/**
 * @ Author     ：maogf
 * @ Date       ：2018/6/26 0026 15:11
 * @ Description：${description}
 */
public class JT808Body {

	private static BitOperator bitOperator = new BitOperator();
	private static BCD8421Operater bcd8421Operater = new BCD8421Operater();
	/**
	 * 实时音视频传输请求
	 * @return
	 */
	public static byte[] get9101Body(VideoPlayParam videoParam) {
		byte[] ipByte = videoParam.getMsg().getSinkIP().getBytes(TPMSConsts.string_charset);//服务器IP
		byte[] n = new byte[] { (byte) ipByte.length };//服务器IP 地址长度
		byte[] tcp = bitOperator.integerTo2Bytes(videoParam.getMsg().getSinkPort());//服务器TCP端口
		byte[] udp = bitOperator.integerTo2Bytes(0);//服务器UDP端口
		byte[] b5 = bitOperator.integerTo1Bytes(videoParam.getMsg().getLogicalChn());// 逻辑通道号
		byte[] b6 = bitOperator.integerTo1Bytes(videoParam.getMsg().getDataType());// 0：音视频，1：视频. 2：双向对讲，3：监听，4：中心广播，5：透传
		byte[] b7 = bitOperator.integerTo1Bytes(videoParam.getMsg().getStreamType());//码流类型
		return BitOperator.concatAll(n, ipByte, tcp, udp, b5, b6, b7);
	}

	/**
	 * 消息ID：0x8801 消息体
	 * 摄像头立即拍摄命令
	 * @return
	 */
	public static byte[] get8801Body(String chnId,String screenNum,String screenInterval,String savaFlag,
									 String resolution,String imgQuality,String bright,
									 String contrast,String saturation,String chroma) {
		byte[] b0 = new byte[]{(byte)Integer.parseInt(chnId)};// 通道 ID
		byte[] b1 = bitOperator.integerTo2Bytes(Integer.parseInt(screenNum));// 拍摄命令,拍照张数
		byte[] b2 = bitOperator.integerTo2Bytes(Integer.parseInt(screenInterval));// 拍照间隔/录像时间
		byte b3 = (byte) Integer.parseInt(savaFlag);// 保存标志
		byte b4 = (byte) Integer.parseInt(resolution);// 分辨率
		byte b5 = (byte) Integer.parseInt(imgQuality);// 图像/视频质量
		byte b6 = (byte) Integer.parseInt(bright);// 亮度
		byte b7 = (byte) Integer.parseInt(contrast);// 对比度
		byte b8 = (byte) Integer.parseInt(saturation);// 饱和度
		byte b9 = (byte) Integer.parseInt(chroma);// 色度
		byte[] b10 = new byte[]{b3,b4,b5,b6,b7,b8,b9};
		return BitOperator.concatAll(b0, b1, b2, b10);
	}

	/**
	 * 消息ID：0x8103 消息体
	 * 终端参数设置
	 * @return
	 */
	public static byte[] get8103Body(String locationStrategy,String locationProject,String heartInterval,
									 String dormancyInterval,String urgentWarningInterval,String defaultInterva){
		byte[] b_num = bitOperator.integerTo1Bytes(6);// 参数总数
		byte[] b_long = bitOperator.integerTo1Bytes(4);//长度
		byte[] b1_1 = bitOperator.integerTo4Bytes(0x0020);// 位置汇报策略ID
		byte[] b1_2 = bitOperator.integerTo4Bytes(Integer.parseInt(locationStrategy));// 位置汇报策略

		byte[] b2_1 = bitOperator.integerTo4Bytes(0x0021);// 位置汇报方案ID
		byte[] b2_2 = bitOperator.integerTo4Bytes(Integer.parseInt(locationProject));// 位置汇报方案

		byte[] b3_1 = bitOperator.integerTo4Bytes(0x0001);// 心跳发送间隔ID
		byte[] b3_2 = bitOperator.integerTo4Bytes(Integer.parseInt(heartInterval));// 心跳发送间隔

		byte[] b4_1 = bitOperator.integerTo4Bytes(0x0027);// 休眠汇报间隔ID
		byte[] b4_2 = bitOperator.integerTo4Bytes(Integer.parseInt(dormancyInterval));// 休眠汇报间隔

		byte[] b5_1 = bitOperator.integerTo4Bytes(0x0028);// 紧急报警汇报间隔ID
		byte[] b5_2 = bitOperator.integerTo4Bytes(Integer.parseInt(urgentWarningInterval));// 紧急报警汇报间隔

		byte[] b6_1 = bitOperator.integerTo4Bytes(0x0029);// 缺省汇报间隔ID
		byte[] b6_2 = bitOperator.integerTo4Bytes(Integer.parseInt(defaultInterva));// 缺省汇报间隔
		return BitOperator.concatAll(b_num,
				b1_1,b_long,b1_2,
				b2_1,b_long,b2_2,
				b3_1,b_long,b3_2,
				b4_1,b_long,b4_2,
				b5_1,b_long,b5_2,
				b6_1,b_long,b6_2);
	}

	/**
	 * 消息ID：0x8400 消息体
	 * 电话回拨
	 * @return
	 */
	public static byte[] get8400Body(String phoneCarType,String phoneNo){
		byte[] b_phoneCarType = bitOperator.integerTo1Bytes(Integer.parseInt(phoneCarType));// 电话回拨标志
		byte[] b_phoneNo = phoneNo.getBytes();// 手机号
		return BitOperator.concatAll(b_phoneCarType,b_phoneNo);
	}

	/**
	 * 音视频实时传输控制
	 * @return
	 */
	public static byte[] get9102Body(VideoPlayParam videoParam) {
		byte[] b1 = bitOperator.integerTo1Bytes(videoParam.getMsg().getLogicalChn());// 逻辑通道号
		byte[] b2 = bitOperator.integerTo1Bytes(videoParam.getMsg().getCtrlCmd());// 控制指令
		byte[] b3 = bitOperator.integerTo1Bytes(videoParam.getMsg().getCloseAVType());// 关闭音视频类型
		byte[] b4 = bitOperator.integerTo1Bytes(videoParam.getMsg().getChangeStreamType());// 切换码流类型
		return BitOperator.concatAll(b1, b2, b3, b4);
	}

	/**
	 * 实时音视频传输状态通知
	 * @return
	 */
	public static byte[] get9105Body(VideoPlayParam videoParam) {
		byte[] b1 = bitOperator.integerTo1Bytes(videoParam.getMsg().getLogicalChn());//逻辑通道号
		byte[] b2 = bitOperator.integerTo1Bytes(videoParam.getMsg().getLossRate());//丢包率
		return BitOperator.concatAll(b1, b2);
	}

	/**
	 * 查询资源列表
	 * @param channel
	 * @return
	 */
	public static byte[] get9205Body(int channel,String startTime,String endTime) {
		byte[] b0 = bitOperator.integerTo1Bytes(channel);// 逻辑通道号
		byte[] b1 = BCD8421Operater.str2Bcd(startTime);
		byte[] b7 = BCD8421Operater.str2Bcd(endTime);
//		byte[] b1 = bitOperator.concatAll(new byte[][]{bitOperator.integerTo3Bytes(0), bitOperator.integerTo3Bytes(0)});
//		byte[] b7 = bitOperator.concatAll(new byte[][]{bitOperator.integerTo3Bytes(0), bitOperator.integerTo3Bytes(0)});
		byte[] b13 = BitOperator.concatAll(bitOperator.integerTo4Bytes(0), bitOperator.integerTo4Bytes(0));
		byte[] b21 = bitOperator.integerTo1Bytes(0);// 音视频资源类型 0：音视频 3：视频或音视频
		byte[] b22 = bitOperator.integerTo1Bytes(0);// 码流类型 0：所有码流
		byte[] b23 = bitOperator.integerTo1Bytes(0);// 存储器类型 0：所有存储器
		return BitOperator.concatAll(b0, b1, b7, b13, b21, b22, b23);
	}

	/**
	 * 平台下发远程录像回放请求
	 * @return
	 */
	public static byte[] get9201Body(VideoParamBean videoParam) {
		String ip = videoParam.getMsg().getSinkIP();
		byte[] b1 = ip.getBytes(TPMSConsts.string_charset);
		byte[] b0 = new byte[] { (byte) b1.length };
		byte[] b1n = bitOperator.integerTo2Bytes(videoParam.getMsg().getSinkPort());//TCP端口
		byte[] b3n = bitOperator.integerTo2Bytes(0);//UDP端口
		byte[] b5n = bitOperator.integerTo1Bytes(videoParam.getMsg().getLogicalChn());// 逻辑通道号
		byte[] b6n = bitOperator.integerTo1Bytes(videoParam.getMsg().getResourceType());// 音视频资源类型 0：音视频，1：音频，2：视频，3：视频或音视频
		byte[] b7n = bitOperator.integerTo1Bytes(videoParam.getMsg().getStreamType());// 码流类型 0：主码流或子码流，1：主码流，2：子码流；
		byte[] b8n = bitOperator.integerTo1Bytes(videoParam.getMsg().getStorageType());// 存储器类型 0：主存储器或灾备存储器，1：主存储器，2：灾备存储器
		byte[] b9n = bitOperator.integerTo1Bytes(videoParam.getMsg().getReplayType());// 回放方式 0：正常回放；
		byte[] b10n = bitOperator.integerTo1Bytes(videoParam.getMsg().getMultiples());// 快进或快退倍数 0：无效；1：1 倍；2：2 倍；
		byte[] b11n = BCD8421Operater.str2Bcd(videoParam.getMsg().getBeginTime());// 开始时间
		byte[] b17n = BCD8421Operater.str2Bcd(videoParam.getMsg().getEndTime());// 结束时间
		return BitOperator.concatAll(b0, b1, b1n, b3n, b5n, b6n, b7n, b8n, b9n, b10n, b11n, b17n);
	}

	/**
	 * 平台下发远程录像回放控制
	 * @return
	 */
	public static byte[] get9202Body(VideoPlayParam videoParam) {
		byte[] b1 = bitOperator.integerTo1Bytes(videoParam.getMsg().getLogicalChn());// 逻辑通道号
		byte[] b2 = bitOperator.integerTo1Bytes(videoParam.getMsg().getCtrlCmd());// 控制指令 0：开始回放,1：暂停回放,2：结束回放,3：快进回放,4：关键帧快退回放,5：拖动回放,6：关键帧播放
		byte[] b3 = bitOperator.integerTo1Bytes(videoParam.getMsg().getMultiples());// 快进或快退倍数
		if(videoParam.getMsg().getReplayLocation() != null){
			byte[] b4 = bcd8421Operater.string2Bcd(videoParam.getMsg().getReplayLocation());// 拖动回放位置
			return BitOperator.concatAll(b1, b2, b3, b4);
		}else {
			return BitOperator.concatAll(b1, b2, b3);
		}
	}
}
