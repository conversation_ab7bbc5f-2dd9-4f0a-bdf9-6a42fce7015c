package com.gjxx.buss.mapper;

import com.gjxx.common.mp.mapper.MpBaseMapper;
import java.util.List;
import java.util.Map;
import com.gjxx.buss.domain.TDevModel;

/**
 * 设备型号信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface TDevModelMapper extends MpBaseMapper<TDevModel> {


    /**
     * 查询设备型号信息列表
     *
     * @param tDevModel 设备型号信息
     * @return 设备型号信息集合
     */
    List<TDevModel> selectTDevModelList(TDevModel tDevModel);

}
