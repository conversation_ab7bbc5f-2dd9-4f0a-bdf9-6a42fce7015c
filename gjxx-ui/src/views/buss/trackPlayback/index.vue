<template>
  <div class="video-container">
    <div class="video-left-board">
      <div class="video-left-board-top">
        <div style="padding-top: 2px;padding-left:5px;">
          <el-input
            v-model="queryParams.vehicleId"
            placeholder="可查询车辆标识"
            clearable
            style="width: 76%;"
            @clear="handleQuery"/>
          <el-button  style="" icon="el-icon-search"  @click="handleQuery"></el-button>
        </div>
      </div>
      <div class="video-left-board-down">
        <el-tree
          ref="myTree"
          :data="treeData"
          :props="props"
          node-key="id"
          :highlight-current="true"
          :load="loadNode"
          :render-content="renderContent"
          lazy
          @node-click="handleNodeClick">
        </el-tree>
      </div>
    </div>

    <div class="video-center-board">
      <div class="video-center-board-top">
        <div class="video-center-board-top-div">
          <div style="float: left;margin-top:3px;margin-left:10px">
            <el-form :model="locationQueryParams" ref="queryForm" size="small" :inline="true" >
              <el-form-item label="轨迹时间" prop="daterangeDate">
                <el-date-picker
                  v-model="daterangeDate"
                  value-format="yyyyMMddHHmmss"
                  type="datetimerange"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :picker-options="pickerOptions"
                  clearable
                ></el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="locationQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <div class="video-center-board-down" id="map-board">

      </div>
      <div v-if="showControl">
        <div class="input-item" >
          <h3>轨迹回放控制</h3>
          <div class="item">
            <el-button type="primary" size="mini" round @click="start">开始动画</el-button>
            <el-button type="primary" size="mini" round @click="pause">暂停动画</el-button>
            <el-button type="primary" size="mini" round @click="resume">继续动画</el-button>
            <el-button type="primary" size="mini" round @click="stop">停止动画</el-button>
          </div>
          <div class="item">
            <h3 style="margin-top: 0px;margin-bottom: 0px;">回放时速：{{ speed }} km/h</h3>
            <el-slider
              v-model="speed"
              :step="10"
              show-stops @change="changeSpeed" :min="10" :max="100">
            </el-slider>
          </div>
        </div>
      </div>
    </div>

    <div>
      <div v-show="loading" class="shade"></div>

      <div v-show="loading"  class="dialog">
        <img src="../../../assets/gif/loading.gif">
      </div>
    </div>
  </div>
</template>

<script>

import {getCarDevTreeNoCHn,getCarDevTreeNoCHnByVehicleId} from "@/api/buss/realTimeGps";
import AMapLoader from "@amap/amap-jsapi-loader";
import moment from "moment";
import {trajectoryQuery} from "@/api/buss/trackPlayback";
import {gaodeUtil} from "@/api/tool/gaodeUtil";

export default {
  name: "TrackPlayback",
  components: {
  },
  computed: {
    // 默认时间
    dateDefault() {
      let date1 = moment().subtract(1, "hours").format("YYYYMMDDHHmmss");
      let date2 = moment().format("YYYYMMDDHHmmss");
      // 通过时间戳计算
      return [date1, date2]
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 车辆设备树
      props: {
        id: 'id',
        carDevId: 'carDevId',
        label: 'label',
        carDevStatus: 'carDevStatus',
        whetherCompanyNode: 'whetherCompanyNode',
        whetherCarDevNode: 'whetherCarDevNode',
        whetherCarDevChnNode: 'whetherCarDevChnNode',
        isLeaf: 'whetherLastLevelNode'
      },
      renderContent(h, { node, data }) {
        if(data.whetherCarDevNode){
          if("01" === data.carDevStatus){
            return (
              <span>
              <svg-icon icon-class="car-online"/>
              <span style="margin-left:3px">{node.label}</span>
            </span>);
          }else {
            return (
              <span>
              <svg-icon icon-class="car-offline"/>
              <span style="margin-left:3px">{node.label}</span>
            </span>);
          }
        }else {
          return (
            <span>
              <i class={node.icon}/>
              <span>{node.label}</span>
            </span>);
        }
      },
      propParams: {
        id: null,
        yesNotCarDevNode: null
      },
      map: null,//地图
      polyline: null,//轨迹线
      marker: null,//当前播放轨迹的点
      path: [],//轨迹点
      currentIndex: 0,//当前播放轨迹的点
      selectCarDevId: null,//选择的车辆设备id
      selectCarNo: null,//选择的车辆车牌号
      newIcon: null,//定位图标
      // 查询参数
      queryParams: {
        vehicleId: null
      },
      // 查询车辆历史轨迹参数
      locationQueryParams: {
        carDevId: null,
        startTime: null,
        endTime: null
      },
      showControl: false,//轨迹动画控制组件
      daterangeDate: [],//时间范围
      speed: 10,//轨迹播放速度 km/h
      pickerOptions: {
        shortcuts: [{
          text: '最近一小时',
          onClick(picker) {
            const end = moment(new Date()).format("YYYYMMDDHHmmss");
            const start = moment(new Date()).subtract(1, "hours").format("YYYYMMDDHHmmss");
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三小时',
          onClick(picker) {
            const end = moment(new Date()).format("YYYYMMDDHHmmss");
            const start = moment(new Date()).subtract(3, "hours").format("YYYYMMDDHHmmss");
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '今天',
          onClick(picker) {
            const date = moment().format("YYYYMMDD");
            const end = date + '235959';
            const start = date + '000000';
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const date = moment().subtract(1, "days").format("YYYYMMDD").trim();
            const end = date + '235959';
            const start = date + '000000';
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      info: {},//轨迹点集合
      treeData: []
    }
  },
  watch: {
  },
  created() {
    this.initMap();
  },
  methods: {
    loadNode(node, resolve) {
      if (node === null || node === undefined || node.level === 0){
        this.propParams.id = null;
        this.propParams.whetherCarDevNode = false;
        getCarDevTreeNoCHn(this.propParams).then(response => {
          this.treeData = response.data;
        });
      }else{
        if (node.data.isLeaf === true){
          return resolve([]);
        }
        this.propParams.id = node.data.id;
        this.propParams.whetherCarDevNode = node.data.whetherCarDevNode;
        this.propParams.whetherCompanyNode = node.data.whetherCompanyNode;

        getCarDevTreeNoCHn(this.propParams).then(response => {
          if(node.data.whetherCompanyNode){
            var label  = node.data.label;
            var label1 = label.split("(")[0];
            var label2 = response.data[0].onlineStatusCount;
            node.data.label = label1+label2;
          }
          return resolve(response.data);
        });
      }
    },
    locationQuery(){
      this.loading = true;
      this.showControl = false;
      this.locationQueryParams.startTime = null;
      this.locationQueryParams.endTime = null;
      this.locationQueryParams.carDevId = null;
      if (null != this.daterangeDate && '' !== this.daterangeDate && 0 !== this.daterangeDate.length) {
        this.locationQueryParams.startTime = this.daterangeDate[0];
        this.locationQueryParams.endTime = this.daterangeDate[1];
      }else {
        this.$message({
          message: '轨迹时间不能为空！',
          type: 'warning'
        });
        this.loading = false;
        return false;
      }
      if (null != this.selectCarDevId && '' !== this.selectCarDevId) {
        this.locationQueryParams.carDevId = this.selectCarDevId;
      }else {
        this.$message({
          message: '请选择车辆！',
          type: 'warning'
        });
        this.loading = false;
        return false;
      }
      trajectoryQuery(this.locationQueryParams).then(response => {
        if(response.data == null || response.data.length == 0){
          this.$message({
            message: '没有查询到轨迹数据！',
            type: 'warning'
          });
          this.loading = false;
          return false;
        }
        this.path = response.data.map(point => [point.longitudeShow, point.latitudeShow]);

        gaodeUtil.createPathSimplifier(this.map, {
          data: [{name: this.selectCarNo, path: this.path}],
          strokeStyle: 'blue'
        });
        this.showControl = true;
      });
      this.loading = false;
    },
    resetQuery() {
      this.daterangeDate = this.dateDefault;
      this.resetForm("queryForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if(this.queryParams.vehicleId === null || this.queryParams.vehicleId === ''|| this.queryParams.vehicleId.trim() === ''){
        this.loadNode();
      }else {
        getCarDevTreeNoCHnByVehicleId(this.queryParams).then(response => {
          this.treeData = response.data;
        });
      }
    },
    initMap(){
      AMapLoader.load(gaodeUtil.loadOption({
        plugins: ['AMap.ToolBar', 'AMap.Driving', 'AMap.MouseTool', 'AMap.PolygonEditor'],
      })).then((AMap) => {
        this.map = new AMap.Map("map-board", gaodeUtil.initOption({}));
        this.map.setFitView();
      }).catch(e => {
        console.log(e);
      })
    },
    start() {
      window.navg.start();// 调用方法开启动画
    },
    stop() {
      window.navg.stop();
    },
    pause() {
      window.navg.pause();
    },
    resume() {
      window.navg.resume();
    },
    changeSpeed(speed) {
      window.navg.setSpeed(10 * speed);
    },
    // 节点单击事件
    handleNodeClick(data) {
      const tree = this.$refs.myTree;
      if(data.whetherCarDevNode){
        this.selectCarDevId = data.id;
        this.selectCarNo = data.label;
        if (tree) {
          tree.setCurrentKey(data.id);
        }
      }else {
        tree.setCurrentKey(null);
        this.selectCarDevId = null;
        this.selectCarNo = null;
      }
    },
  }
}
</script>

<style lang='scss' scoped>

.input-item{
  position: absolute;
  background: none;
  padding: 5px 20px 10px;
  right: 10px;
  bottom: 0;
  font-size: 12px;
}
.input-item .item {
  margin: 10px 0;
}
.el-slider__runway{
  background-color: #7397df!important;
}
.shade{
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.dialog{
  z-index: 891016; top: 45%; left: 50%;position:fixed;
}
.video-button{
  background-color: #e6a23c;
  color: white;
  height: 36px;
  margin-left: 10px
}
.video-select{
  width: 130px;
  margin-left:10px;
  font-size: 12px;
}
.video-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 84px);
}

.video-left-board {
  width: 260px;
  position: absolute;
  left: 0;
  top: 0;
  height: calc(100vh - 84px);
}

.video-left-board-top{
  background-color: #E8EAED;
  position: relative;
  height: 42px;
  //background: #fff;
  border-bottom: 1px solid rgb(214, 214, 214);
  border-right: 1px solid rgb(214, 214, 214);
  box-sizing: border-box;
}

.video-left-board-down{
  border-right: 1px solid rgb(214, 214, 214);
  overflow: auto;
  position: relative;
  height: calc(100vh - 126px);
  background: #fff;
  box-sizing: border-box;
}
/* 设置::-webkit-scrollbar可以让滚动条消失，但仍可以滚动 */
//.video-left-board-down::-webkit-scrollbar {
//  display:none
//}
.video-center-board {
  height: calc(100vh - 84px);
  width: auto;
  margin: 0 0 0 260px;
  box-sizing: border-box;
}
.video-center-board-top{
  background-color: #E8EAED;
  position: relative;
  height: 42px;
  box-sizing: border-box;
  border-top: none;
  border-bottom:1px solid #d6d6d6;
}
.video-center-board-down {
  overflow: hidden;
  position: relative;
  height: calc(100vh - 126px);
  background: #fff;
  box-sizing: border-box;
}
.el-form-item--small{
  margin-bottom: 0!important;
  margin-top: 1px!important;
}

</style>
