@echo off & setlocal enabledelayedexpansion
rem nrvsp-system windows start.bat
title tsmis-service
rem dos utf-8 encode
chcp 65001

set LIB_JARS=""

cd ..\lib
for %%i in (*) do set LIB_JARS=!LIB_JARS!..\lib\%%i
title %LIB_JARS%
cd ..\bin

if ""%1"" == ""debug"" goto debug
if ""%1"" == ""jmx"" goto jmx

java -Dfile.encoding=UTF-8 -Xms64m -Xmx1024m -XX:MaxPermSize=64M -jar %LIB_JARS%
goto end

:debug
java -Dfile.encoding=UTF-8 -Xms64m -Xmx1024m -XX:MaxPermSize=64M -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,address=8000,server=y,suspend=n -jar %LIB_JARS%
goto end

:jmx
java -Dfile.encoding=UTF-8 -Xms64m -Xmx1024m -XX:MaxPermSize=64M -Dcom.sun.management.jmxremote.port=1099 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -jar %LIB_JARS%

:end
pause