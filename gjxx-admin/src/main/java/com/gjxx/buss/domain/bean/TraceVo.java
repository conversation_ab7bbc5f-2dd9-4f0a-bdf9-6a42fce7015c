package com.gjxx.buss.domain.bean;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 车辆定位信息展示结果数据
 */
@Data
public class TraceVo implements Serializable {

    private static final long serialVersionUID = -4382713791484047390L;

    /**
     * sim卡号
     */
    private String simNo;
    /**
     * 定位时间
     */
    private String positionTime;

    /**
     * 经度，单位为 1*10^-6 度。
     */
    private long longitude;

    /**
     * 纬度，单位为 1*10^-6 度。
     */
    private long latitude;
    /**
     * 经度。
     */
    private BigDecimal longitudeShow;

    /**
     * 纬度。
     */
    private BigDecimal latitudeShow;

}
