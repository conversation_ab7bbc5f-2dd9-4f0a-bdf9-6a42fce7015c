package com.gjxx;

import com.gjxx.netty.MyNettyServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.Async;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class GjxxApplication implements CommandLineRunner {
    public static void main(String[] args) {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        log.info(">>>>>>>>>>>>>> gjxx-admin 开始初始化 >>>>>>>>>>>>>>");
        SpringApplication.run(GjxxApplication.class, args);
        log.info(">>>>>>>>>>>>>> gjxx-admin 初始化完成 >>>>>>>>>>>>>>");
    }

    @Async
    @Override
    public void run(String... args) throws Exception {
        /*
         * 使用异步注解方式启动netty服务端服务
         * 7788   监听端口
         */
        new MyNettyServer().startNettyServer(7788);
    }
}
