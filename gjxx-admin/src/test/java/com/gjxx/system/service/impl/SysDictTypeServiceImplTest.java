package com.gjxx.system.service.impl;

import com.gjxx.BaseServiceTest;
import com.gjxx.common.core.domain.entity.SysDictType;
import com.gjxx.system.service.ISysDictTypeService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SysDictTypeServiceImplTest extends BaseServiceTest {

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Test
    void getById() {
        SysDictType byId = this.dictTypeService.getById(1L);
        System.out.println(byId);
    }

    @Test
    void selectDictTypeById() {
        SysDictType byId = this.dictTypeService.selectDictTypeById(1L);
        System.out.println(byId);
    }
}
