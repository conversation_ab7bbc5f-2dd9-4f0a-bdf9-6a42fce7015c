package com.gjxx.common.utils.old;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2017/5/23
 */
public class ObjectUtil {
    /**
     * 判断是否为空
     *
     * @param v
     * @return
     */
    public static boolean isNull(Object v) {
        if (v == null||"".equals(v)) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否为空
     *
     * @param v
     * @return
     */
    public static boolean isNotNull(Object v) {
        return !isNull(v);
    }

    /**
     * 判断对象属性是否为空
     *
     * @param v
     * @return
     */
    public static <T> boolean isFieldValueNull(T v) {

        for (Field f : v.getClass().getDeclaredFields()) {
            f.setAccessible(true);
            try {
                Object o = f.get(v);
//                System.out.println("o = " + o);
                if (!"serialVersionUID".equals(f.getName()) && f.get(v) != null && !"".equals(f.get(v))) { //判断字段是否为空，并且对象属性中的基本都会转为对象类型来判断
                    return false;
                }
            } catch (IllegalArgumentException | IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return true;

    }

    public static <T> boolean isFieldValueNotNull(T v) {

        return !isFieldValueNull(v);
    }


    /**
     * 动态获取对象中某属性值
     *
     * @param item
     * @param name
     * @return
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     * @throws NoSuchFieldException
     * @throws SecurityException
     */
    @SuppressWarnings("unchecked")
    public static <T, C> C getFieldValue(T item, String name, Class<C> clazz) {

        if (item instanceof Map) {
            Map<String, Object> itemMap = (Map<String, Object>) item;
            return (C) itemMap.get(name);
        }


        C value = null;

        Field field = null;

        try {

            field = item.getClass().getDeclaredField(name);

            field.setAccessible(true);

            value = (C) field.get(item);

        } catch (NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException e) {

            e.printStackTrace();

        }

        return value;
    }

    /**
     * 查询list集合对象中某个属性的集合
     *
     * @param list
     * @param fieldName
     * @param clazz
     * @return
     */
    public static <T, C> List<C> getFieldValues(List<T> list, String fieldName, Class<C> clazz) {

        List<C> result = new ArrayList<C>();

        if (Collections3.isEmpty(list)) return result;

        for (T item : list) {
            C value = ObjectUtil.getFieldValue(item, fieldName, clazz);
            result.add(value);
        }

        return result;
    }

    /**
     * 获取到对象中属性为null的属性名
     *
     * @param source 要拷贝的对象
     * @return
     */
    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    /**
     * 获取对象和所有父类 Class 集合
     *
     * @param obj
     * @return
     */
    public static List<Class> getClassList(Object obj) {
        List<Class> classes = new ArrayList<>();
        Class<?> objClass = obj.getClass();
        while (objClass != null) {
            classes.add(objClass);
            objClass = objClass.getSuperclass();
        }
        return classes;
    }

    /**
     * 拷贝非空对象属性值
     *
     * @param source 源对象
     * @param target 目标对象
     */
    public static void copyPropertiesIgnoreNull(Object source, Object target) {
        BeanUtils.copyProperties(source, target, getNullPropertyNames(source));
    }

}
