package com.gjxx.netty.codec;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;

/**
 * JT808协议转义工具类
 * 支持JT/T808-2013和JT/T808-2019版本
 *
 * <pre>
 * 0x7d01 <====> 0x7d
 * 0x7d02 <====> 0x7e
 * </pre>
 *
 * <AUTHOR>
 *
 */
public class JT808ProtocolUtils {
	
	/**
	 * JT808协议版本枚举
	 */
	public enum ProtocolVersion {
		V2013("2013", "JT/T808-2013"),
		V2019("2019", "JT/T808-2019");
		
		private final String version;
		private final String description;
		
		ProtocolVersion(String version, String description) {
			this.version = version;
			this.description = description;
		}
		
		public String getVersion() {
			return version;
		}
		
		public String getDescription() {
			return description;
		}
	}
	private final Logger log = LoggerFactory.getLogger(getClass());
	private BitOperator bitOperator;
	private BCD8421Operater bcd8421Operater;

	public JT808ProtocolUtils() {
		this.bitOperator = new BitOperator();
		this.bcd8421Operater = new BCD8421Operater();
	}

	/**
	 * 接收消息时转义<br>
	 *
	 * <pre>
	 *  0x7d01 <====> 0x7d
	 *  0x7d02 <====> 0x7e
	 * </pre>
	 *
	 * @param bs
	 *            要转义的字节数组
	 * @param start
	 *            起始索引
	 * @param end
	 *            结束索引
	 * @return 转义后的字节数组
	 * @throws Exception
	 */
	public static byte[] doEscape4Receive(byte[] bs, int start, int end) throws Exception {
		if (start < 0 || end > bs.length)
			throw new ArrayIndexOutOfBoundsException("doEscape4Receive error : index out of bounds(start=" + start
					+ ",end=" + end + ",bytes length=" + bs.length + ")");
		ByteArrayOutputStream baos = null;
		try {
			baos = new ByteArrayOutputStream();
			for (int i = 0; i < start; i++) {
				baos.write(bs[i]);
			}
			for (int i = start; i < end - 1; i++) {
				if (bs[i] == 0x7d && bs[i + 1] == 0x01) {
					baos.write(0x7d);
					i++;
				} else if (bs[i] == 0x7d && bs[i + 1] == 0x02) {
					baos.write(0x7e);
					i++;
				} else {
					baos.write(bs[i]);
				}
			}
			for (int i = end - 1; i < bs.length; i++) {
				baos.write(bs[i]);
			}
			return baos.toByteArray();
		} catch (Exception e) {
			throw e;
		} finally {
			if (baos != null) {
				baos.close();
				baos = null;
			}
			}
	}
	
	/**
	 * 接收消息时转义（兼容JT/T808-2013和JT/T808-2019协议）<br>
	 * 自动检测协议版本并进行相应的转义处理
	 *
	 * <pre>
	 *  0x7d01 <====> 0x7d
	 *  0x7d02 <====> 0x7e
	 * </pre>
	 *
	 * @param bs 要转义的字节数组（完整的JT808消息包，包含起始符0x7e和结束符0x7e）
	 * @return 转义后的字节数组
	 * @throws Exception
	 */
	public static byte[] doEscape4ReceiveCompatible(byte[] bs) throws Exception {
		if (bs == null || bs.length < 5) {
			throw new IllegalArgumentException("Invalid message data: too short");
		}
		
		// 检查起始和结束标志
		if (bs[0] != 0x7e || bs[bs.length - 1] != 0x7e) {
			throw new IllegalArgumentException("Invalid message format: missing start/end flag 0x7e");
		}
		
		// 去掉起始和结束的0x7e标志，对中间部分进行转义
		return doEscape4Receive(bs, 1, bs.length - 1);
	}

	/**
	 *
	 * 发送消息时转义<br>
	 *
	 * <pre>
	 *  0x7e <====> 0x7d02
	 *  0x7d <====> 0x7d01
	 * </pre>
	 *
	 * @param bs
	 *            要转义的字节数组
	 * @param start
	 *            起始索引
	 * @param end
	 *            结束索引
	 * @return 转义后的字节数组
	 * @throws Exception
	 */
	public byte[] doEscape4Send(byte[] bs, int start, int end) throws Exception {
		if (start < 0 || end > bs.length)
			throw new ArrayIndexOutOfBoundsException("doEscape4Send error : index out of bounds(start=" + start
					+ ",end=" + end + ",bytes length=" + bs.length + ")");
		ByteArrayOutputStream baos = null;
		try {
			baos = new ByteArrayOutputStream();
			for (int i = 0; i < start; i++) {
				baos.write(bs[i]);
			}
			for (int i = start; i < end; i++) {
				if (bs[i] == 0x7e) {
					baos.write(0x7d);
					baos.write(0x02);
				}else if(bs[i] == 0x7d){
					baos.write(0x7d);
					baos.write(0x01);
				}else{
					baos.write(bs[i]);
				}
			}
			for (int i = end; i < bs.length; i++) {
				baos.write(bs[i]);
			}
			return baos.toByteArray();
		} catch (Exception e) {
			throw e;
		} finally {
			if (baos != null) {
				baos.close();
				baos = null;
			}
		}
	}

	/**
	 * 生成消息体属性 - JT/T808-2013版本
	 * @param msgLen 消息体长度
	 * @param enctyptionType 加密类型
	 * @param isSubPackage 是否有子包
	 * @param reversed_14_15 保留位
	 * @return 消息体属性
	 */
	public int generateMsgBodyProps(int msgLen, int enctyptionType, boolean isSubPackage, int reversed_14_15) {
		return generateMsgBodyProps(msgLen, enctyptionType, isSubPackage, reversed_14_15, ProtocolVersion.V2013);
	}
	
	/**
	 * 生成消息体属性 - 支持多版本
	 * @param msgLen 消息体长度
	 * @param enctyptionType 加密类型
	 * @param isSubPackage 是否有子包
	 * @param reversed_14_15 保留位(2013版本) 或 版本标识(2019版本)
	 * @param version 协议版本
	 * @return 消息体属性
	 */
	public int generateMsgBodyProps(int msgLen, int enctyptionType, boolean isSubPackage, int reversed_14_15, ProtocolVersion version) {
		if (version == ProtocolVersion.V2019) {
			return generateMsgBodyProps2019(msgLen, enctyptionType, isSubPackage, reversed_14_15);
		} else {
			return generateMsgBodyProps2013(msgLen, enctyptionType, isSubPackage, reversed_14_15);
		}
	}
	
	/**
	 * 生成消息体属性 - JT/T808-2013版本
	 */
	private int generateMsgBodyProps2013(int msgLen, int enctyptionType, boolean isSubPackage, int reversed_14_15) {
		// [ 0-9 ] 0000,0011,1111,1111(3FF)(消息体长度)
		// [10-12] 0001,1100,0000,0000(1C00)(加密类型)
		// [ 13_ ] 0010,0000,0000,0000(2000)(是否有子包)
		// [14-15] 1100,0000,0000,0000(C000)(保留位)
		if (msgLen >= 1024)
			log.warn("The max value of msgLen is 1023, but {} .", msgLen);
		int subPkg = isSubPackage ? 1 : 0;
		int ret = (msgLen & 0x3FF) | ((enctyptionType << 10) & 0x1C00) | ((subPkg << 13) & 0x2000)
				| ((reversed_14_15 << 14) & 0xC000);
		return ret & 0xffff;
	}
	
	/**
	 * 生成消息体属性 - JT/T808-2019版本
	 */
	private int generateMsgBodyProps2019(int msgLen, int enctyptionType, boolean isSubPackage, int versionFlag) {
		// [ 0-9 ] 0000,0011,1111,1111(3FF)(消息体长度)
		// [10-12] 0001,1100,0000,0000(1C00)(加密类型)
		// [ 13_ ] 0010,0000,0000,0000(2000)(是否有子包)
		// [14-15] 1100,0000,0000,0000(C000)(版本标识位，2019版本固定为01)
		if (msgLen >= 1024)
			log.warn("The max value of msgLen is 1023, but {} .", msgLen);
		int subPkg = isSubPackage ? 1 : 0;
		// 2019版本的版本标识位固定为01
		int versionBit = 0x01;
		int ret = (msgLen & 0x3FF) | ((enctyptionType << 10) & 0x1C00) | ((subPkg << 13) & 0x2000)
				| ((versionBit << 14) & 0xC000);
		return ret & 0xffff;
	}

	/**
	 * 生成消息头 - JT/T808-2013版本
	 * @param phone 终端手机号
	 * @param msgType 消息类型
	 * @param body 消息体
	 * @param msgBodyProps 消息体属性
	 * @param flowId 消息流水号
	 * @return 消息头字节数组
	 * @throws Exception
	 */
	public byte[] generateMsgHeader(String phone, int msgType, byte[] body, int msgBodyProps, int flowId)
			throws Exception {
		return generateMsgHeader(phone, msgType, body, msgBodyProps, flowId, ProtocolVersion.V2013);
	}
	
	/**
	 * 生成消息头 - 支持多版本
	 * @param phone 终端手机号
	 * @param msgType 消息类型
	 * @param body 消息体
	 * @param msgBodyProps 消息体属性
	 * @param flowId 消息流水号
	 * @param version 协议版本
	 * @return 消息头字节数组
	 * @throws Exception
	 */
	public byte[] generateMsgHeader(String phone, int msgType, byte[] body, int msgBodyProps, int flowId, ProtocolVersion version)
			throws Exception {
		if (version == ProtocolVersion.V2019) {
			return generateMsgHeader2019(phone, msgType, body, msgBodyProps, flowId);
		} else {
			return generateMsgHeader2013(phone, msgType, body, msgBodyProps, flowId);
		}
	}
	
	/**
	 * 生成消息头 - JT/T808-2013版本
	 */
	private byte[] generateMsgHeader2013(String phone, int msgType, byte[] body, int msgBodyProps, int flowId)
			throws Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = new ByteArrayOutputStream();
			// 1. 消息ID word(16)
			baos.write(bitOperator.integerTo2Bytes(msgType));
			// 2. 消息体属性 word(16)
			baos.write(bitOperator.integerTo2Bytes(msgBodyProps));
			// 3. 终端手机号 bcd[6]
			baos.write(bcd8421Operater.string2Bcd(phone));
			// 4. 消息流水号 word(16),按发送顺序从 0 开始循环累加
			baos.write(bitOperator.integerTo2Bytes(flowId));
			// 消息包封装项 此处不予考虑
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}
	
	/**
	 * 生成消息头 - JT/T808-2019版本
	 */
	private byte[] generateMsgHeader2019(String phone, int msgType, byte[] body, int msgBodyProps, int flowId)
			throws Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = new ByteArrayOutputStream();
			// 1. 消息ID word(16)
			baos.write(bitOperator.integerTo2Bytes(msgType));
			// 2. 消息体属性 word(16)
			baos.write(bitOperator.integerTo2Bytes(msgBodyProps));
			// 3. 协议版本号 byte(8) - 2019版本新增
			baos.write(0x01); // 2019版本固定为0x01
			// 4. 终端手机号 bcd[10] - 2019版本扩展为10字节
			byte[] phoneBytes = bcd8421Operater.string2Bcd(phone);
			if (phoneBytes.length < 10) {
				// 不足10字节的前面补0
				byte[] paddedPhone = new byte[10];
				System.arraycopy(phoneBytes, 0, paddedPhone, 10 - phoneBytes.length, phoneBytes.length);
				baos.write(paddedPhone);
			} else {
				// 超过10字节的截取后10字节
				baos.write(phoneBytes, phoneBytes.length - 10, 10);
			}
			// 5. 消息流水号 word(16),按发送顺序从 0 开始循环累加
			baos.write(bitOperator.integerTo2Bytes(flowId));
			// 消息包封装项 此处不予考虑
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}
	
	/**
	 * 从消息体属性中解析协议版本
	 * @param msgBodyProps 消息体属性
	 * @return 协议版本
	 */
	public static ProtocolVersion parseProtocolVersion(int msgBodyProps) {
		// 检查版本标识位 [14-15]
		int versionBits = (msgBodyProps & 0xC000) >> 14;
		if (versionBits == 0x01) {
			return ProtocolVersion.V2019;
		} else {
			return ProtocolVersion.V2013;
		}
	}
	
	/**
	 * 从原始数据中快速判断协议版本
	 * @param data 原始字节数组
	 * @return 协议版本
	 */
	public static ProtocolVersion detectProtocolVersion(byte[] data) {
		if (data == null || data.length < 4) {
			return ProtocolVersion.V2013; // 默认返回2013版本
		}
		
		try {
			// 解析消息体属性 (byte[2-3])
			int msgBodyProps = ((data[2] & 0xFF) << 8) | (data[3] & 0xFF);
			return parseProtocolVersion(msgBodyProps);
		} catch (Exception e) {
			return ProtocolVersion.V2013; // 异常时默认返回2013版本
		}
	}
	
	/**
	 * 获取消息头长度
	 * @param version 协议版本
	 * @param hasSubPackage 是否有子包
	 * @return 消息头长度
	 */
	public static int getMsgHeaderLength(ProtocolVersion version, boolean hasSubPackage) {
		int baseLength;
		if (version == ProtocolVersion.V2019) {
			baseLength = 17; // 2019版本基础消息头长度：2+2+1+10+2=17
		} else {
			baseLength = 12; // 2013版本基础消息头长度：2+2+6+2=12
		}
		
		if (hasSubPackage) {
			baseLength += 4; // 子包信息：2+2=4字节
		}
		
		return baseLength;
	}
	
	/**
	 * 解析终端手机号的起始位置
	 * @param version 协议版本
	 * @return 手机号在消息头中的起始位置
	 */
	public static int getPhoneStartIndex(ProtocolVersion version) {
		if (version == ProtocolVersion.V2019) {
			return 5; // 2019版本：消息ID(2) + 消息体属性(2) + 协议版本(1) = 5
		} else {
			return 4; // 2013版本：消息ID(2) + 消息体属性(2) = 4
		}
	}
	
	/**
	 * 获取终端手机号的长度
	 * @param version 协议版本
	 * @return 手机号字段长度
	 */
	public static int getPhoneLength(ProtocolVersion version) {
		if (version == ProtocolVersion.V2019) {
			return 10; // 2019版本：BCD[10]
		} else {
			return 6;  // 2013版本：BCD[6]
		}
	}
}
