<template>
  <div class="video-container">
    <div class="video-left-board">
      <div class="video-left-board-top">
        <div style="padding-top: 2px;padding-left:5px;">
          <el-input
            v-model="queryParams.vehicleId"
            placeholder="可查询车辆标识"
            clearable
            style="width: 76%;"
            @clear="handleQuery"/>
          <el-button  style="" icon="el-icon-search"  @click="handleQuery"></el-button>
        </div>
      </div>
      <div class="video-left-board-down">
        <el-tree
          ref="myTree"
          :data="treeData"
          :props="props"
          node-key="id"
          :highlight-current="true"
          :load="loadNode"
          :render-content="renderContent"
          lazy
          @node-click="handleNodeClick">
        </el-tree>
      </div>
    </div>

    <div class="video-center-board" ref="fullscreenDiv">
      <div class="video-center-board-top">
        <div class="video-center-board-top-div">
          <div style="float: left;margin-top:3px">
            <el-button class="video-button" size="mini" @click="allShutdown" >全部关闭</el-button>
          </div>
          <div style="float: left;margin-top:3px">
            <el-select
              v-model="streamType"
              placeholder="码流类型"
              class="video-select">
              <el-option
                v-for="dict in dict.type.t_stream_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"/>
            </el-select>

            <el-select
              v-model="vedioWindowNum"
              placeholder="视频通道数"
              @change="vedioChnNumChange"
              class="video-select">
              <el-option
                v-for="dict in dict.type.t_vedio_chn_num"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"/>
            </el-select>
          </div>
          <div style="float: right;margin-top:7px;margin-right:5px;font-size: 24px;">
            <span style="cursor:pointer;" @click="screenfullClick">
                <svg-icon :icon-class="isFullscreen?'video-full-screen-cancel':'video-full-screen'" />
            </span>
          </div>
          <div style="float: right;margin-top:12px;margin-right:15px;font-size: 14px;">
            <span>下一播放窗口:</span>
            <span style="color: red">{{nextPlayNum}}</span>
          </div>
        </div>

      </div>
      <div :class="{ videoCenterBoardDown: isFullscreen }" style="overflow: hidden;position: relative;
                height: calc(100vh - 126px);background: #fff;
                box-sizing: border-box;">
        <div v-for="(item, index) in divs" :key="index" :style="{width: myVideoWidth,height: myVideoHeight}" class="my-video-wrapper"
             :class="{active: activeIndex === index }" @click="changeBorder(index)">
          <div class="player-wraper" >
            <div style="width: 100%;height: 100%;" :id="'video'+index" ></div>
          </div>
          <div class="my-video-control">
            <div class="device-states">
              <span>{{ index + 1 }}.</span>
              <span :id="'video-span'+index"></span>
            </div>
            <div class="video-controls">
              <span>
                <svg-icon :icon-class="getIconClass(index)" :key="iconKey" @click="playStop(index)" />
              </span>
              <span>
                 <svg-icon :icon-class="getVideoVoiceIconClass(index)" :key="videoVoiceIconKey" @click="videoVoice(index)"/>
              </span>
              <span>
                <svg-icon icon-class="video-photo" @click="videoPhoto(index)"/>
              </span>
              <span>
                <svg-icon icon-class="video-full-screen" @click="videoFullScreen(index)" />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="video-right-board">
      <div id="video-right-board-top" style="height: 55%;">

      </div>
      <div style="height: 45%;">
        <table class="table" border="1" bordercolor="#eee">
          <colgroup>
            <col width="40%">
            <col>
          </colgroup>
          <tbody>
          <tr class="right-tr">
            <td class="right-td">车辆标识：</td>
            <td>{{ carDevDetail.vehicleId }}</td>
          </tr>
          <tr>
            <td class="right-td">车牌颜色：</td>
            <td>{{ carDevDetail.vehicleNoColor }}</td>
          </tr>
          <tr class="right-tr">
            <td class="right-td">SIM卡号：</td>
            <td>{{ carDevDetail.simNo }}</td>
          </tr>
          <tr>
            <td class="right-td">所属公司：</td>
            <td>{{ carDevDetail.carTeamName }}</td>
          </tr>
          <tr class="right-tr">
            <td class="right-td">车辆状态：</td>
            <td>{{ carDevDetail.carDevStatus }}</td>
          </tr>
          <tr>
            <td class="right-td">定位时间：</td>
            <td>{{ carDevDetail.posTime }}</td>
          </tr>
          <tr class="right-tr">
            <td class="right-td">定位地址：</td>
            <td>{{ carDevDetail.locationAddress }}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import {
  carDevAndVedioPlay, FLVPlayer,
  getCarDevTree,
  getCarDevTreeByVehicleId
} from "@/api/buss/realTimeVedio";
import AMapLoader from "@amap/amap-jsapi-loader";
import icon from '../../../assets/images/map-car.png';
import $ from 'jquery';
import screenfull from "screenfull";

export default {
  name: "RealTimeVedio",
  dicts: ['t_stream_type','t_vedio_chn_num','t_vehicle_no_color'],
  components: {
  },
  data() {
    return {
      // 车辆设备树
      props: {
        id: 'id',
        carDevId: 'carDevId',
        label: 'label',
        carDevStatus: 'carDevStatus',
        whetherCompanyNode: 'whetherCompanyNode',
        whetherCarDevNode: 'whetherCarDevNode',
        whetherCarDevChnNode: 'whetherCarDevChnNode',
        isLeaf: 'whetherLastLevelNode'
      },
      iconMap: new Map(),//播放图标map
      iconKey: 0,//播放图标key
      videoVoiceIconMap: new Map(),//声音图标map
      videoVoiceIconKey: 0,//声音图标key
      isFullscreen: false,//是否全屏
      streamType: "1",//码流类型 1-子码流，0-主码流
      vedioWindowNum: "9",//视频播放窗口数
      videoMap: new Map(), // 视频map，key-video标签ID，value-FLVPlayer
      videophoneChnMap: new Map(), // map，key-video标签ID，value-手机号通道
      phoneChnMap: new Map(), // map，key-手机号通道，value-video标签ID
      fristPortMap: new Map(), // 第一个端口map，key-video标签ID，value-端口号
      twoPortMap: new Map(), // 第二个端口map，key-video标签ID，value-端口号
      threePortMap: new Map(), // 第三个端口map，key-video标签ID，value-端口号
      fourPortMap: new Map(), // 第四个端口map，key-video标签ID，value-端口号
      fivePortMap: new Map(), // 第五个端口map，key-video标签ID，value-端口号
      propParams: {//查询车辆设备树结构参数
        id: null,
        whetherCarDevNode: null
      },
      queryParams: {//根据车辆标识查询车辆树参数
        vehicleId: null
      },
      treeData: [],//车辆树数据
      activeIndex: null, // 当前激活的div索引
      divs: Array(25).fill(null), // 生成25个div
      nextPlayNum: 1,// 下一播放窗口
      map: null,//地图
      marker: null,//地图标记
      currentLocation: null,//当前定位
      // 车辆设备详情
      carDevDetail: {
        vehicleId: null,
        vehicleNoColor: null,
        simNo: null,
        carTeamName: null,
        carDevStatus: null,
        posTime: null,
        locationAddress: null,
        accStatus: null,
        warningStatus: null,
        vedioChnNum: null,
        flvPlayer: null
      },
      // 视频播放
      videoPlayParams: {
        carDevId: null,
        vedioChnNum: null,
        streamType: null
      },
      // 停止视频播放
      stopVideoPlayParams: {
        simNo: null,
        vedioChnNum: null
      },
      renderContent(h, { node, data }) {
        if(data.whetherCarDevNode){
          if("01" === data.carDevStatus){
            return (
              <span>
              <svg-icon icon-class="car-online"/>
              <span style="margin-left:3px">{node.label}</span>
            </span>);
          }else {
            return (
              <span>
              <svg-icon icon-class="car-offline"/>
              <span style="margin-left:3px">{node.label}</span>
            </span>);
          }
        }else if(data.whetherCarDevChnNode){
          if("01" === data.carDevStatus){
            return (
              <span>
              <svg-icon icon-class="video-onlile"/>
              <span style="margin-left:3px">{node.label}</span>
            </span>);
          }else {
            return (
              <span>
              <svg-icon icon-class="video-offlile"/>
              <span style="margin-left:3px">{node.label}</span>
            </span>);
          }
        }else {
          return (
            <span>
              <i class={node.icon}/>
              <span>{node.label}</span>
            </span>);
        }
      },
      newIcon: null,//定位图标
      myVideoWidth: "33.333%",//视频播放窗口宽度
      myVideoHeight: "33.333%"//视频播放窗口高度
    }
  },
  watch: {
  },
  created() {
    this.initMap();
  },
  mounted() {
    this.screenfullInit();
  },
  beforeDestroy() {
    this.screenfullDestroy();
  },
  methods: {
    loadNode(node, resolve) {
      if (node === null || node === undefined || node.level === 0){
        this.propParams.id = null;
        this.propParams.whetherCarDevNode = false;
        getCarDevTree(this.propParams).then(response => {
          this.treeData = response.data;
        });
      }else{
        if (node.data.isLeaf === true){
          return resolve([]);
        }
        this.propParams.id = node.data.id;
        this.propParams.whetherCarDevNode = node.data.whetherCarDevNode;
        this.propParams.whetherCompanyNode = node.data.whetherCompanyNode;

        getCarDevTree(this.propParams).then(response => {
          if(node.data.whetherCompanyNode){
            var label  = node.data.label;
            var label1 = label.split("(")[0];
            var label2 = response.data[0].onlineStatusCount;
            node.data.label = label1+label2;
          }
          return resolve(response.data);
        });
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if(this.queryParams.vehicleId === null || this.queryParams.vehicleId === ''|| this.queryParams.vehicleId.trim() === ''){
          this.loadNode();
      }else {
        // this.treeData = []; // 清空树的数据
        getCarDevTreeByVehicleId(this.queryParams).then(response => {
          this.treeData = response.data;
        });
      }
    },
    initMap(){
      AMapLoader.load({
        key: process.env.VUE_APP_MAP_KEY,
        version: "2.0",
        plugins: ['AMap.GeoJSON', 'AMap.Marker'] // 引入地理定位和标记点插件
      }).then((AMap) => {
          // 调用地图渲染函数
          // renderMap(AMap);
          this.map = new AMap.Map("video-right-board-top", {  //设置地图容器id
            viewMode: "2D",    //是否为3D地图模式
            zoom: 12,           //初始化地图级别
            center: this.currentLocation//初始化地图中心点位置
          });

          // 创建自定义图标
          this.newIcon = new AMap.Icon({
            size: new AMap.Size(35, 20), // 图标尺寸
            image: icon, // 图标的URL
            imageSize: new AMap.Size(35, 20), // 图标图片尺寸
          });

        // 添加自定义图标标记
          this.marker = new AMap.Marker({
            position: this.currentLocation,
            icon: this.newIcon,
            offset: new AMap.Pixel(-25, -25)
          });
          this.map.add(this.marker);

        }).catch((e) => {
          console.error(e); //加载错误提示
        });
      // const renderMap = (AMap) => {
      //   this.map = new AMap.Map("video-right-board-top", {
      //     zoom: 12,
      //     center: this.currentLocation
      //   });
      // };
    },
    changeBorder(index) {
      this.activeIndex = index; // 设置当前激活的div索引
      this.nextPlayNum = index +1;
    },
    /** 视频通道数选择 */
    vedioChnNumChange(value){
      this.vedioWindowNum = value;
      const valuerNum = Number(value);
      if(valuerNum === 1){
        this.myVideoWidth = "100%";
        this.myVideoHeight = "100%";
      }else if(valuerNum === 4){
        this.myVideoWidth = "50%";
        this.myVideoHeight = "50%";
      }else if(valuerNum === 9){
        this.myVideoWidth = "33.333%";
        this.myVideoHeight = "33.333%";
      }else if(valuerNum === 16){
        this.myVideoWidth = "25%";
        this.myVideoHeight = "25%";
      }else if(valuerNum === 25){
        this.myVideoWidth = "20%";
        this.myVideoHeight = "20%";
      }
      if(this.nextPlayNum > valuerNum){
        this.nextPlayNum = 1;
      }
      this.activeIndex = null;
      for (let i = valuerNum; i < 25; i++) {
        if(this.videoMap != null){
          if(this.videoMap.get(i) != null){
            this.videoMap.get(i).destroy();
            $("#video"+i).empty();
            $("#video-span"+i).empty();
            this.videoMap.delete(i);
          }
        }
        if(this.videophoneChnMap != null){
          const phoneChn = this.videophoneChnMap.get(i);
          if(phoneChn != null){
            this.phoneChnMap.delete(phoneChn);
          }
        }
        if(this.fristPortMap != null){
          this.fristPortMap.delete(i);
        }
        if(this.twoPortMap != null){
          this.twoPortMap.delete(i);
        }
        if(this.threePortMap != null){
          this.threePortMap.delete(i);
        }
        if(this.fourPortMap != null){
          this.fourPortMap.delete(i);
        }
        if(this.fivePortMap != null){
          this.fivePortMap.delete(i);
        }
        if(this.iconMap != null){
          this.iconMap.delete(i);
        }
        if(this.videoVoiceIconMap != null){
          this.videoVoiceIconMap.delete(i);
        }
      }
    },
    // 全部关闭
    allShutdown(){
      for (let i = 0; i < 25; i++) {
        if(this.videoMap != null){
          if(this.videoMap.get(i) != null){
            this.videoMap.get(i).destroy();
            $("#video"+i).empty();
            $("#video-span"+i).empty();
            this.videoMap.delete(i);
          }
        }
      }
      this.videophoneChnMap = new Map();
      this.phoneChnMap = new Map();
      this.fristPortMap = new Map();
      this.twoPortMap = new Map();
      this.threePortMap = new Map();
      this.fourPortMap = new Map();
      this.fivePortMap = new Map();
      this.iconMap = new Map();
      this.videoVoiceIconMap = new Map();
      this.nextPlayNum = 1;
      this.activeIndex = null;
    },
    getIconClass(id) {
      return this.iconMap.get(id) || 'video-play'; // 默认图标类名
    },
    getVideoVoiceIconClass(id) {
      return this.videoVoiceIconMap.get(id) || 'video-voice-open'; // 默认图标类名
    },
    // 节点单击事件
    handleNodeClick(data) {
      const tree = this.$refs.myTree;
      if(data.whetherCarDevChnNode){
        if("01" === data.carDevStatus){
          this.videoPlayParams.carDevId = data.carDevId;
          this.videoPlayParams.vedioChnNum = data.id;
          this.videoPlayParams.streamType = this.streamType;
          carDevAndVedioPlay(this.videoPlayParams).then(response => {
            const responseData = response.data;
            this.map.remove(this.marker);
            this.currentLocation = [responseData.longitude,responseData.latitude];
            this.map.setCenter(this.currentLocation);
            this.marker = new AMap.Marker({
              position: this.currentLocation,
              icon: this.newIcon,
              offset: new AMap.Pixel(-10, -10)
            });
            this.map.add(this.marker);

            this.carDevDetail.vehicleId = responseData.vehicleId;
            this.carDevDetail.vehicleNoColor = this.selectDictLabel(
                  this.dict.type.t_vehicle_no_color, responseData.vehicleNoColor);
            this.carDevDetail.simNo = responseData.simNo;
            this.carDevDetail.carTeamName = responseData.carTeamName;
            this.carDevDetail.carDevStatus = responseData.carDevStatus;
            this.carDevDetail.posTime = responseData.posTime;
            this.carDevDetail.locationAddress = responseData.locationAddress;
            this.carDevDetail.accStatus = responseData.accStatus;
            this.carDevDetail.warningStatus = responseData.warningStatus;
            this.carDevDetail.vedioChnNum = responseData.vedioChnNum;
            let bool = true;
            let videoId = this.nextPlayNum - 1;
            if(this.phoneChnMap != null){
              let videoIdValue = this.phoneChnMap.get(responseData.simNo+responseData.vedioChnNum);
              if(videoIdValue !== undefined && videoIdValue != null){
                videoIdValue = Number(videoIdValue);
                bool = false;
                const index = videoIdValue + 1;
                const vedioWindowNumNum = Number(this.vedioWindowNum);
                if(index > vedioWindowNumNum){
                  bool = true;
                  this.videophoneChnMap.delete(videoIdValue);
                  this.phoneChnMap.delete(responseData.simNo+responseData.vedioChnNum);
                }else {
                  // 元素不是空的
                  this.$message({
                    message: '该视频已经在第'+index+'窗口播放',
                    type: 'warning'
                  });
                  videoId = videoIdValue;
                }
              }
            }
            var flvPlayerValue = this.videoMap.get(videoId);
            if(flvPlayerValue != null){
              flvPlayerValue.destroy();
              $("#video"+videoId).empty();
              this.videoMap.delete(videoId);
              if(this.videophoneChnMap.get(videoId) != null){
                this.phoneChnMap.delete(this.videophoneChnMap.get(videoId));
              }
              this.videophoneChnMap.delete(videoId);
              this.fristPortMap.delete(videoId);
              this.twoPortMap.delete(videoId);
              this.threePortMap.delete(videoId);
              this.fourPortMap.delete(videoId);
              this.fivePortMap.delete(videoId);
              this.iconMap.delete(videoId);
              this.videoVoiceIconMap.delete(videoId);
            }
            var playUrl = "http://"+responseData.sinkIP+":"+responseData.sinkPlayPort+responseData.playRelativeUrl;
            if(this.fristPortMap.size < 6){
              playUrl = "http://"+responseData.sinkIP+":"+responseData.sinkPlayPort+responseData.playRelativeUrl;
              this.fristPortMap.set(videoId,responseData.sinkPlayPort);
            }else if(this.twoPortMap.size < 6){
              playUrl = "http://"+responseData.sinkPlayIP+":"+responseData.sinkPlayTwoPort+responseData.playRelativeUrl;
              this.twoPortMap.set(videoId,responseData.sinkPlayTwoPort);
            }else if(this.threePortMap.size < 6){
              playUrl = "http://"+responseData.sinkPlayIP+":"+responseData.sinkPlayThreePort+responseData.playRelativeUrl;
              this.threePortMap.set(videoId,responseData.sinkPlayThreePort);
            }else if(this.fourPortMap.size < 6){
              playUrl = "http://"+responseData.sinkPlayIP+":"+responseData.sinkPlayFourPort+responseData.playRelativeUrl;
              this.fourPortMap.set(videoId,responseData.sinkPlayFourPort);
            }else if(this.fivePortMap.size < 6){
              playUrl = "http://"+responseData.sinkPlayIP+":"+responseData.sinkPlayFivePort+responseData.playRelativeUrl;
              this.fivePortMap.set(videoId,responseData.sinkPlayFivePort);
            }
            console.log("playUrl==="+playUrl)
            var flvPlayer = new FLVPlayer({
              container : $('#video'+videoId),
              url : playUrl,
              // 自动快进追祯，但是可能会导致画面停顿
              autoFastForward : false
            });
            flvPlayer.play();
            this.videoMap.set(videoId, flvPlayer);
            this.videophoneChnMap.set(videoId,responseData.simNo+responseData.vedioChnNum);
            this.phoneChnMap.set(responseData.simNo+responseData.vedioChnNum, videoId);
            $("#video-span"+videoId).html(responseData.vehicleId+"-"+responseData.vedioChnNum);
            this.iconMap.set(videoId, "video-play-stop");
            if(bool){
              const vedioChnNumNum = Number(this.vedioWindowNum);
              const nextPlayNumNum = Number(this.nextPlayNum);
              if(vedioChnNumNum === nextPlayNumNum){
                this.nextPlayNum = 1;
              }else {
                this.nextPlayNum = this.nextPlayNum + 1;
              }
            }
          });
          this.iconKey++;
          this.videoVoiceIconKey++;
          if (tree) {
            tree.setCurrentKey(data.id);
          }
        }else {
          tree.setCurrentKey(null);
          this.$message({
            message: '该设备未在线',
            type: 'warning'
          });
        }
      }else {
        tree.setCurrentKey(null);
      }
    },
    //停止播放
    playStop(videoId){
      if(this.iconMap.get(videoId) != null){
        const flvPlayerValue = this.videoMap.get(videoId);
        if(flvPlayerValue != null){
          flvPlayerValue.destroy();
          $("#video"+videoId).empty();
          $("#video-span"+videoId).empty();
          if(this.videophoneChnMap.get(videoId) != null){
            const phoneChn = this.videophoneChnMap.get(videoId);
            if(this.phoneChnMap.get(phoneChn) != null){
              this.phoneChnMap.delete(phoneChn);
            }
          }
          this.videophoneChnMap.delete(videoId);
          this.videoMap.delete(videoId);
          this.fristPortMap.delete(videoId);
          this.twoPortMap.delete(videoId);
          this.threePortMap.delete(videoId);
          this.fourPortMap.delete(videoId);
          this.fivePortMap.delete(videoId);
          this.iconMap.delete(videoId);
          this.videoVoiceIconMap.delete(videoId);
          this.iconKey++;
          this.videoVoiceIconKey++;
        }
      }
    },
    //声音控制
    videoVoice(index){
      const parentElement = document.getElementById('video'+index);
      if (parentElement.children && parentElement.children.length > 0) {
        const firstChild = parentElement.children[0];
        if (firstChild.muted) {
          firstChild.muted = false;
          this.videoVoiceIconMap.set(index, "video-voice-open");
        } else {
          firstChild.muted = true;
          this.videoVoiceIconMap.set(index, "video-voice-close");
        }
        this.videoVoiceIconKey++;
      }
    },
    //截图保存
    videoPhoto(index) {
      const parentElement = document.getElementById('video'+index);
      if (parentElement.children && parentElement.children.length > 0) {
        const firstChild = parentElement.children[0];
        const canvas = document.createElement('canvas');
        canvas.width = firstChild.videoWidth;
        canvas.height = firstChild.videoHeight;
        canvas.getContext('2d').drawImage(firstChild, 0, 0, canvas.width, canvas.height);

        canvas.toBlob((blob) => {
          if (blob) {
            // 创建一个URL指向blob对象
            const url = URL.createObjectURL(blob);
            // 创建一个a标签用于下载
            const link = document.createElement('a');
            link.href = url;
            link.download = 'screenshot.png';
            link.click();
            // 清除URL对象
            URL.revokeObjectURL(url);
          }
        }, 'image/png');
      }
    },
    //播放窗口全屏
    videoFullScreen(videoId){
      const parentElement = document.getElementById('video'+videoId);
      if (parentElement.children && parentElement.children.length > 0) {
        const firstChild = parentElement.children[0];
        if (firstChild.requestFullscreen) {
          firstChild.requestFullscreen();
        } else if (firstChild.mozRequestFullScreen) { // Firefox
          firstChild.mozRequestFullScreen();
        } else if (firstChild.webkitRequestFullscreen) { // Chrome, Safari and Opera
          firstChild.webkitRequestFullscreen();
        } else if (firstChild.msRequestFullscreen) { // IE/Edge
          firstChild.msRequestFullscreen();
        }
      }
    },
    screenfullChange() {
      this.isFullscreen = screenfull.isFullscreen;
    },
    screenfullInit() {
      if (screenfull.isEnabled) {
        screenfull.on('change', this.screenfullChange)
      }
    },
    screenfullDestroy() {
      if (screenfull.isEnabled) {
        screenfull.off('change', this.screenfullChange)
      }
    },
    //全屏
    screenfullClick() {
      const elem = this.$refs.fullscreenDiv;
      if (elem.requestFullscreen) {
        if(this.isFullscreen){
          document.exitFullscreen();
        }else {
          elem.requestFullscreen();
        }
      } else if (elem.mozRequestFullScreen) { // Firefox
        if(this.isFullscreen){
          document.mozCancelFullScreen();
        }else {
          elem.mozRequestFullScreen();
        }
      } else if (elem.webkitRequestFullscreen) { // Chrome, Safari and Opera
        if(this.isFullscreen){
          document.webkitExitFullscreen();
        }else {
          elem.webkitRequestFullscreen();
        }
      } else if (elem.msRequestFullscreen) { // IE/Edge
        if(this.isFullscreen){
          document.msExitFullscreen();
        }else {
          elem.msRequestFullscreen();
        }
      }
    }
  }
}
</script>

<style lang='scss' scoped>

.active {
  border: 1px dashed blue; /* 激活的div的样式 */
}

.player-wraper{
  position: absolute;
  left: 0;
  right: 0;
  bottom: 34px;
  top: 0;
  overflow: hidden;
}
//.player-wraper:focus{
//  border: 5px solid #ff0000;  /* 设置自定义边框颜色 */
//}
.my-video-control{
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 35px;
  background-color: #000;
  color: #fff;
}
.my-video-control .device-states{
  float: left;
  height: 35px;
  line-height: 35px;
  padding-left: 5px;
  position: absolute;
  left: 0;
  top: 0;
  font-size: 12px;
  color: #c1c2c6;
}
.my-video-control .video-controls{
  height: 35px;
  line-height: 35px;
  font-size: 20px;
  position: absolute;
  right: 0;
  top: 0;
  width: 140px;
  padding-left: 5px;
  background-color: #000;
  user-select: none;
}
.my-video-control .video-controls>span{
  cursor: pointer;
  color: #c1c2c6;
  margin: 0 4px 0 13px;
}
.svg-icon{
  width: 0.8em !important;
  height: 0.8em !important;
}

.iconfont{
  font-family: iconfont !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
}
.player-wraper>video{
  width: 100%;
  height: 100%;
  object-fit: fill;
}


.video-button{
  background-color: #e6a23c;
  color: white;
  height: 36px;
  margin-left: 10px
}
.video-select{
  width: 130px;
  margin-left:10px;
  font-size: 12px;
}
.video-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-left-board {
  width: 260px;
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  //display: flex;
}

.video-left-board-top{
  background-color: #E8EAED;
  position: relative;
  height: 42px;
  //background: #fff;
  border-bottom: 1px solid rgb(214, 214, 214);
  border-right: 1px solid rgb(214, 214, 214);
  box-sizing: border-box;
}

.video-left-board-down{
  border-right: 1px solid rgb(214, 214, 214);
  overflow: auto;
  position: relative;
  height: calc(100vh - 126px);
  background: #fff;
  box-sizing: border-box;
}
/* 设置::-webkit-scrollbar可以让滚动条消失，但仍可以滚动 */
//.video-left-board-down::-webkit-scrollbar {
//  display:none
//}
.video-center-board {
  height: 100%;
  width: auto;
  margin: 0 300px 0 260px;
  box-sizing: border-box;
}
.video-center-board-top{
  background-color: #E8EAED;
  position: relative;
  height: 42px;
  box-sizing: border-box;;
  border-top: none;
}

.videoCenterBoardDown{
  height: calc(100vh - 42px)!important;
}

.video-right-board {
  width: 300px;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  .table {
    width: 100%;
    height: 100%;
    margin: 0;
    background-color: #fff;
    color: #5f5f5f;
    border-collapse: collapse;
  }
  .right-td{
    text-align: right;
  }
  .right-tr{
    background-color: #f9f9f9;
  }
  .table td,
  .table th {
    position: relative;
    padding: 10px 15px;
    min-height: 20px;
    line-height: 20px;
    font-size: 14px;
    word-break: break-all;
  }
  //.table tr {
  //  &:hover {
  //    background-color: #f9f9f9;
  //  }
  //}
}

.my-video-wrapper{
  float: left;
  position: relative;
  background-image: url(../../../assets/images/bg-video.jpg);
  background-size: 100% 100%;
  background-position: 50%;
}
.screenfull-svg {
  display: inline-block;
  cursor: pointer;
  fill: #5a5e66;;
  width: 20px;
  height: 20px;
  vertical-align: 10px;
}
</style>
