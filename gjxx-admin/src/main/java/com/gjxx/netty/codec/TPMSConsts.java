package com.gjxx.netty.codec;

import java.nio.charset.Charset;

public class TPMSConsts {

	public static final String string_encoding = "GBK";

	public static final Charset string_charset = Charset.forName(string_encoding);
	// 标识位
	public static final int pkg_delimiter = 0x7e;
	// 客户端发呆30分钟后,服务器主动断开连接
	public static int tcp_client_idle_minutes = 30;

	// 终端通用应答
	public static final int msg_id_terminal_common_resp = 0x0001;
	// 终端心跳
	public static final int msg_id_terminal_heart_beat = 0x0002;
	// 终端注册
	public static final int msg_id_terminal_register = 0x0100;
	// 终端注销
	public static final int msg_id_terminal_log_out = 0x0003;
	// 终端鉴权
	public static final int msg_id_terminal_authentication = 0x0102;
	// 位置信息汇报
	public static final int msg_id_terminal_location_info_upload = 0x0200;
	// 胎压数据透传
	public static final int msg_id_terminal_transmission_tyre_pressure = 0x0600;
	// 查询终端参数应答
	public static final int msg_id_terminal_param_query_resp = 0x0104;
	// 位置信息查询应答
	public static final int msg_id_location_query_resp = 0x0201;
	// 摄像头立即拍摄命令应答
	public static final int msg_id_photo_screen_resp = 0x0805;
	// 多媒体数据上传
	public static final int msg_id_media_file_upload = 0x0801;
	// 终端注册应答
	public static final int msg_id_terminal_register_resp = 0x8100;
	// 终端上传音视频资源列表
	public static final int msg_id_terminal_video_list_resp = 0x1205;
	// 终端上传音视频属性
	public static final int msg_id_terminal_video_upload_attributes = 0x1003;

	// 平台通用应答
	public static final int cmd_common_resp = 0x8001;
	// 设置终端参数
	public static final int cmd_terminal_param_settings = 0x8103;
	// 查询终端参数
	public static final int cmd_terminal_param_query = 0x8104;
	// 位置信息查询
	public static final int cmd_location_query = 0x8201;
	// 文本信息下发
	public static final int cmd_text_send = 0x8300;
	// 电话回拨
	public static final int cmd_phone_call = 0x8400;
	// 摄像头立即拍摄命令
	public static final int cmd_photo_screen = 0x8801;
	// 多媒体数据上传应答
	public static final int cmd_media_file_upload_resp = 0x8800;
	// 实时音视频传输请求
	public static final int cmd_video_request = 0x9101;
	// 音视频实时传输控制
	public static final int cmd_video_control = 0x9102;
	// 实时音视频传输状态通知
	public static final int cmd_video_status = 0x9105;
	// 查询历史音视频资源列表
	public static final int cmd_history_video = 0x9205;
	// 平台下发远程录像回放请求
	public static final int cmd_video_playback_request = 0x9201;
	// 平台下发远程录像回放控制
	public static final int cmd_video_playback_control = 0x9202;
	// 平台查询终端音视频属性
	public static final int cmd_terminal_video_attributes = 0x9003;

}
