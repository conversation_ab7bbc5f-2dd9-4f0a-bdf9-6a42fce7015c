FROM openjdk:8
MAINTAINER gjxx
COPY target/gjxx-video-server.jar app.jar
RUN bash -c "touch /app.jar"
ENV JAVA_OPTS="-Duser.timezone=GMT+08 -Xms256M -Xmx512M -XX:+HeapDumpOnOutOfMemoryError -XX:+PrintGCDateStamps  -XX:+PrintGCDetails -XX:NewRatio=1 -XX:SurvivorRatio=30 -XX:+UseParallelGC -XX:+UseParallelOldGC"
EXPOSE 8080
ENTRYPOINT [ "sh", "-c", "exec java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar /app.jar" ]
