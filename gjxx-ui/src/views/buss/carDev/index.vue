<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="车辆标识" prop="vehicleId">
        <el-input
          v-model="queryParams.vehicleId"
          placeholder="请输入车辆标识"
          clearable
          @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="车牌颜色" prop="vehicleNoColor">
          <el-select
            v-model="queryParams.vehicleNoColor"
            placeholder="车牌颜色"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in dict.type.t_vehicle_no_color"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
      </el-form-item>
      <el-form-item label="SIM卡号" prop="simNo">
        <el-input
          v-model="queryParams.simNo"
          placeholder="请输入SIM卡号"
          clearable
          @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="隶属车队" prop="carTeamId">
        <treeselect v-model="queryParams.carTeamId" :options="carTeamOptions" :show-count="true" placeholder="请选择隶属车队" style="width: 240px" />
      </el-form-item>
      <el-form-item label="设备型号" prop="devModelId">
        <el-select v-model="queryParams.devModelId" filterable  placeholder="请选择设备型号" style="width: 100%">
          <el-option
            v-for="item in devModelOptions"
            :key="item.id"
            :label="item.devModel"
            :value="item.id"
            :disabled="item.status == 1"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['buss:carDev:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['buss:carDev:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['buss:carDev:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['buss:carDev:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="carDevList" @selection-change="handleSelectionChange" @sort-change="handleSortChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="车辆标识" align="center" prop="vehicleId" />
      <el-table-column label="车牌颜色" align="center" prop="vehicleNoColor">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_vehicle_no_color" :value="scope.row.vehicleNoColor"/>
        </template>
      </el-table-column>
      <el-table-column label="SIM卡号" align="center" prop="simNo" />
      <el-table-column label="隶属车队" align="center" prop="carTeamId">
        <template slot-scope="scope">
          <span>{{carTeamMap[scope.row.carTeamId]}}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备型号" align="center" prop="devModelId">
        <template slot-scope="scope">
          <span>{{devModelMap[scope.row.devModelId]}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180"  align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['buss:carDev:edit']">修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['buss:carDev:remove']">删除</el-button>
          <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row,scope.index)"
              v-hasPermi="['buss:carDev:query']">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"/>

      <!-- 添加或修改车辆设备信息对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="45%" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="车辆标识" prop="vehicleId">
              <el-input v-model="form.vehicleId" placeholder="请输入车辆标识" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车牌颜色" prop="vehicleNoColor">
              <el-select v-model="form.vehicleNoColor" placeholder="请选择车牌颜色" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.t_vehicle_no_color"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="SIM卡号" prop="simNo">
              <el-input v-model="form.simNo" placeholder="请输入SIM卡号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="隶属车队" prop="carTeamId">
              <treeselect v-model="form.carTeamId" :options="carTeamOptions" :show-count="true" placeholder="请选择隶属车队" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备型号" prop="devModelId">
              <el-select v-model="form.devModelId" filterable  placeholder="请选择设备型号" style="width: 100%">
                <el-option
                  v-for="item in devModelOptions"
                  :key="item.id"
                  :label="item.devModel"
                  :value="item.id"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="视频通道" prop="vedioChList">
              <el-checkbox-group v-model="form.vedioChList" >
                <el-checkbox label="1">CH-1</el-checkbox>
                <el-checkbox label="2">CH-2</el-checkbox>
                <el-checkbox label="3">CH-3</el-checkbox>
                <el-checkbox label="4">CH-4</el-checkbox>
                <el-checkbox label="5">CH-5</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

    <!-- 车辆设备信息详情对话框 -->
    <el-dialog title="车辆设备信息详情" :visible.sync="details" width="60%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="180px">
        <el-row>
            <el-col :span="24">
              <el-form-item label="车辆标识：">{{ form.vehicleId }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车牌颜色：">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.t_vehicle_no_color" :value="form.vehicleNoColor"/>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="SIM卡号：">{{ form.simNo }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="隶属车队：">{{ carTeamMap[form.carTeamId] }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备型号：">{{ devModelMap[form.devModelId] }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="视频通道：" >
                <el-checkbox-group v-model="form.vedioChList" >
                  <el-checkbox label="1" disabled>CH-1</el-checkbox>
                  <el-checkbox label="2" disabled>CH-2</el-checkbox>
                  <el-checkbox label="3" disabled>CH-3</el-checkbox>
                  <el-checkbox label="4" disabled>CH-4</el-checkbox>
                  <el-checkbox label="5" disabled>CH-5</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="创建人：">{{ form.createUser }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="创建时间：">{{ parseTime(form.createTime) }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="修改人：">{{ form.updateUser }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="修改时间：">{{ parseTime(form.updateTime) }}</el-form-item>
            </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="details = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCarDev, getCarDev, delCarDev,
  addCarDev, updateCarDev, carTeamTreeSelect, getDevModelList, getCarTeamMap, getDevModelMap
} from "@/api/buss/carDev";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "CarDev",
  dicts: ['t_vehicle_no_color'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 车辆设备信息表格数据
      carDevList: [],
      // 弹出层标题
      title: "",
      // 车队树选项
      carTeamOptions: undefined,
      // 设备型号选项
      devModelOptions: [],
      // 车队map
      carTeamMap: new Map(),
      // 设备型号map
      devModelMap: new Map(),
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层
      details: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        vehicleId: null,
        vehicleNoColor: null,
        simNo: null,
        carTeamId: null,
        devModelId: null
      },
      // 表单参数
      form: {
        vedioChList: [],
      },
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 表单校验
      rules: {
        vehicleId: [
          { required: true, message: "车辆标识不能为空", trigger: "blur" },
          { min: 7, max: 32, message: '车辆标识长度必须介于 7 和 32 之间', trigger: 'blur' }
        ],
        vehicleNoColor: [
          { required: true, message: "车牌颜色不能为空", trigger: "blur" }
        ],
        simNo: [
          { required: true, message: "SIM卡号不能为空", trigger: "blur" },
          { min: 11, max: 32, message: 'SIM卡号长度必须介于 11 和 32 之间', trigger: 'blur' }
        ],
        carTeamId: [
          { required: true, message: "隶属车队不能为空", trigger: "blur" }
        ],
        devModelId: [
          { required: true, message: "设备型号不能为空", trigger: "blur" }
        ],
        vedioChList: [{
          required: true,
          type: 'array',
          message: '请至少选择一个视频通道',
          trigger: 'blur'
        }],
      }
    };
  },
  created() {
    getDevModelList().then(response => {
      this.devModelOptions = response.devModels;
    });
    getCarTeamMap().then(response => {
      this.carTeamMap = response.carTeamMap;
    });
    getDevModelMap().then(response => {
      this.devModelMap = response.devModelMap;
    });
    this.getCarTeamTree();
    this.getList();
  },
  methods: {
    /** 查询车辆设备信息列表 */
    getList() {
      this.loading = true;
      listCarDev(this.queryParams).then(response => {
        this.carDevList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询车队下拉树结构 */
    getCarTeamTree() {
      carTeamTreeSelect().then(response => {
        this.carTeamOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.carTeamId = data.id;
      this.handleQuery();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        vehicleId: null,
        vehicleNoColor: null,
        simNo: null,
        carTeamId: null,
        devModelId: null,
        vedioChList: [],
        createUser: null,
        createTime: null,
        updateUser: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    // 动态取值排序
    handleSortChange(column) {
      this.queryParams.orderByColumn = column.prop;//查询字段是表格中字段名字
      this.queryParams.isAsc = column.order;//动态取值排序顺序
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      getDevModelList().then(response => {
        this.devModelOptions = response.devModels;
        this.open = true;
        this.title = "添加车辆设备信息";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCarDev(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改车辆设备信息";
      });
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.details = true;
      this.form = row;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCarDev(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCarDev(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除车辆设备信息？').then(function() {
        return delCarDev(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('buss/carDev/export', {
        ...this.queryParams
      }, `carDev_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
