<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gjxx.buss.mapper.TWarningMapper">

    <update id="handleBatchByIds" parameterType="java.util.List">
        update t_warning set handle_status = '02', handle_user = #{userName},handle_time = now()
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="handleNoByIds" parameterType="java.util.List">
        update t_warning set handle_status = '03', handle_user = #{userName},handle_time = now()
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectTWarningList" parameterType="com.gjxx.buss.domain.TWarning" resultType="com.gjxx.buss.domain.TWarning">
        select a.*,b.vehicle_id from t_warning a left join t_car_dev b on a.sim_no = b.sim_no
        <where>
            <if test="simNo != null  and simNo != ''">
                and a.sim_no = #{simNo}
            </if>
            <if test="warningType != null  and warningType != ''">
                and a.warning_type = #{warningType}
            </if>
            <if test="warningCount != null ">
                and a.warning_count = #{warningCount}
            </if>
            <if test="handleStatus != null  and handleStatus != ''">
                and a.handle_status = #{handleStatus}
            </if>
            <if test="handleUser != null  and handleUser != ''">
                and a.handle_user = #{handleUser}
            </if>
            <if test="vehicleId != null  and vehicleId != ''">
                and b.vehicle_id = #{vehicleId}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(a.create_time,'%y%m%d%h%i%s') &gt;= date_format(#{params.beginTime},'%y%m%d%h%i%s')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(a.create_time,'%y%m%d%h%i%s') &lt;= date_format(#{params.endTime},'%y%m%d%h%i%s')
            </if>
        </where>
    </select>
</mapper>
