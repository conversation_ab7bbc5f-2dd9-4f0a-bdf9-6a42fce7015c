<template>
  <div class="video-container">
    <div class="video-left-board">
      <div class="video-left-board-top">
        <div style="padding-top: 2px;padding-left:5px;">
          <el-input
            v-model="queryParams.vehicleId"
            placeholder="可查询车辆标识"
            clearable
            style="width: 76%;"
            @clear="handleQuery"/>
          <el-button  style="" icon="el-icon-search"  @click="handleQuery"></el-button>
        </div>
      </div>
      <div class="video-left-board-down">
        <el-tree
          ref="myTree"
          :data="treeData"
          :props="props"
          node-key="id"
          :highlight-current="true"
          :load="loadNode"
          :render-content="renderContent"
          lazy
          @node-click="handleNodeClick">
        </el-tree>
      </div>
    </div>

    <div class="video-center-board">
      <el-tabs type="border-card" style="height: calc(100vh - 84px);">
        <el-tab-pane label="立即拍照">
          <div style="margin: 2%;">
            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="对比度" prop="contrast">
                    <el-input v-model="form.contrast" step="1" min="0" 	max="127" type="number" placeholder="请输入对比度" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="亮度" prop="bright">
                    <el-input v-model="form.bright" step="1" min="0" 	max="255"  type="number" placeholder="请输入亮度" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="饱和度" prop="saturation">
                    <el-input v-model="form.saturation" step="1" min="0" 	max="127" type="number"  placeholder="请输入饱和度" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="色度" prop="chroma">
                    <el-input v-model="form.chroma" step="1" min="0" 	max="255" type="number"  placeholder="请输入色度" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="拍摄通道" prop="chnId">
                    <el-select
                      v-model="form.chnId"
                      placeholder="拍摄通道"
                      clearable
                      style="width: 100%">
                      <el-option
                        v-for="dict in dict.type.t_vedio_ch"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分辨率" prop="resolution">
                    <el-select v-model="form.resolution" placeholder="请选择分辨率" style="width: 100%">
                      <el-option label="320*240" value="01" ></el-option>
                      <el-option label="640*480" value="02"></el-option>
                      <el-option label="800*600" value="03"></el-option>
                      <el-option label="1024*768" value="04"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="图像质量" prop="imgQuality">
                    <el-input v-model="form.imgQuality" step="1" min="1" 	max="10" type="number" placeholder="请输入图像质量" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <div style="text-align: center;margin-top: 2%">
              <el-button type="primary" @click="submitForm">立即拍照</el-button>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="文本下发">
          <div style="margin: 2%;">
            <el-form ref="textForm" :model="textForm" :rules="textRules" label-width="100px">
              <el-row>
                <el-col :span="6">
                  <el-form-item label="信息标志" prop="msgType">
                    <div>
                      <el-radio v-model="textForm.msgType" label="0" >紧急</el-radio>
                      <br/>
                      <el-radio v-model="textForm.msgType" label="2" >终端显示器显示</el-radio>
                      <br/>
                      <el-radio v-model="textForm.msgType" label="3" >TTS播读</el-radio>
                      <br/>
                      <el-radio v-model="textForm.msgType" label="4" >广告屏显示</el-radio>
                      <br/>
                      <el-radio v-model="textForm.msgType" label="5" >CAN故障码信息</el-radio>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="文本信息" prop="text">
                    <el-input
                      type="textarea"
                      :rows="8"
                      placeholder="请输入文本信息"
                      v-model="textForm.text">
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <div style="margin-top: 2%">
              <el-button style="margin-left: 10%;margin-top: 2%" type="primary" @click="textSubmitForm">文本信息下发</el-button>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="终端参数">
          <div style="margin-top: 3%;margin-right: 3%;">
            <el-form ref="paramForm" :model="paramForm" :rules="paramRules" label-width="180px">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="位置汇报策略" prop="locationStrategy">
                    <el-select v-model="paramForm.locationStrategy" placeholder="请选择位置汇报策略" style="width: 100%">
                      <el-option label="定时汇报" value="0" ></el-option>
                      <el-option label="定距汇报" value="1"></el-option>
                      <el-option label="定时和定距汇报" value="2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="位置汇报方案" prop="locationProject">
                    <el-select v-model="paramForm.locationProject" placeholder="请选择位置汇报方案" style="width: 100%">
                      <el-option label="根据ACC状态" value="0" ></el-option>
                      <el-option label="根据登录状态和ACC状态" value="1"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="心跳发送间隔(秒)" prop="heartInterval">
                    <el-input v-model="paramForm.heartInterval" type="number"  placeholder="请输入心跳发送间隔" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="休眠汇报间隔(秒)" prop="dormancyInterval">
                    <el-input v-model="paramForm.dormancyInterval" type="number"  placeholder="请输入休眠汇报间隔" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="缺省汇报间隔(秒)" prop="defaultInterval">
                    <el-input v-model="paramForm.defaultInterval" type="number" placeholder="缺省汇报间隔" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="紧急报警汇报间隔(秒)" prop="urgentWarningInterval">
                    <el-input v-model="paramForm.urgentWarningInterval" type="number" placeholder="请输入紧急报警汇报间隔" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <div style="text-align: center;margin-top: 3%">
              <el-button type="primary" @click="paramSelectSubmitForm">参数查询</el-button>
              <el-button type="primary" @click="paramSubmitForm" style="margin-left: 30px;">参数设置</el-button>
            </div>
          </div>
        </el-tab-pane>
<!--        <el-tab-pane label="电话回拨">-->
<!--          <div style="margin: 3%;">-->
<!--            <el-form ref="phoneForm" :model="phoneForm" :rules="phoneRules" label-width="150px">-->
<!--              <el-row>-->
<!--                <el-col :span="24">-->
<!--                  <el-form-item label="电话回拨标志" prop="phoneCarType">-->
<!--                    <el-select v-model="phoneForm.phoneCarType" placeholder="请选择电话回拨标志" style="width: 80%">-->
<!--                      <el-option label="监听" value="1" ></el-option>-->
<!--                      <el-option label="语音对讲" value="0"></el-option>-->
<!--                    </el-select>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--                <el-col :span="24">-->
<!--                  <el-form-item label="回拨电话号码" prop="phoneNo">-->
<!--                    <el-input v-model="phoneForm.phoneNo" type="text"  placeholder="请输入回拨电话号码"  style="width: 80%"/>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--              </el-row>-->
<!--            </el-form>-->
<!--            <div style="margin-top: 3%">-->
<!--              <el-button style="margin-left: 10%;margin-top: 2%" type="primary" @click="phoneSubmitForm">电话回拨</el-button>-->
<!--            </div>-->
<!--          </div>-->
<!--        </el-tab-pane>-->
      </el-tabs>
    </div>
    <div>
      <div v-show="loading" class="shade"></div>

      <div v-show="loading"  class="dialog">
        <img src="../../../assets/gif/loading.gif">
      </div>
    </div>
  </div>
</template>

<script>

import {getCarDevTreeNoCHn, getCarDevTreeNoCHnByVehicleId} from "@/api/buss/realTimeGps";
import {paramSelect, paramSet, phoneCar, photoScreen, textSend} from "@/api/buss/orderDis";

export default {
  name: "OrderDis",
  dicts: ['t_vedio_ch'],
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 车辆设备树
      props: {
        id: 'id',
        carDevId: 'carDevId',
        label: 'label',
        carDevStatus: 'carDevStatus',
        whetherCompanyNode: 'whetherCompanyNode',
        whetherCarDevNode: 'whetherCarDevNode',
        whetherCarDevChnNode: 'whetherCarDevChnNode',
        isLeaf: 'whetherLastLevelNode'
      },
      renderContent(h, { node, data }) {
        if(data.whetherCarDevNode){
          if("01" === data.carDevStatus){
            return (
              <span>
              <svg-icon icon-class="car-online"/>
              <span style="margin-left:3px">{node.label}</span>
            </span>);
          }else {
            return (
              <span>
              <svg-icon icon-class="car-offline"/>
              <span style="margin-left:3px">{node.label}</span>
            </span>);
          }
        }else {
          return (
            <span>
              <i class={node.icon}/>
              <span>{node.label}</span>
            </span>);
        }
      },
      // 立即拍照参数
      form: {
        contrast: 100,
        bright: 100,
        saturation: 100,
        chroma: 100,
        chnId: "1",
        resolution: "02",
        imgQuality: 10,
        selectCarDevId: null,
      },
      // 文本信息下发参数
      textForm: {
        msgType: "0",
        text: null,
        selectCarDevId: null
      },
      // 设备参数查询参数
      paramSelectForm: {
        selectCarDevId: null
      },
      // 设备参数设置参数
      paramForm: {
        locationStrategy: null,
        locationProject: null,
        heartInterval: null,
        dormancyInterval: null,
        urgentWarningInterval: null,
        defaultInterval: null,
        selectCarDevId: null,
      },
      // 电话回拨参数
      phoneForm: {
        phoneCarType: "1",
        phoneNo: null,
        selectCarDevId: null,
      },
      // 立即拍照表单校验
      rules: {
        contrast: [
          { required: true, message: "对比度不能为空", trigger: "blur" },
          { validator: this.validateNumber0d127, trigger: 'blur' }
        ],
        bright: [
          { required: true, message: "亮度不能为空", trigger: "blur" },
          { validator: this.validateNumber0d255, trigger: 'blur' }
        ],
        saturation: [
          { required: true, message: "饱和度不能为空", trigger: "blur" },
          { validator: this.validateNumber0d127, trigger: 'blur' }
        ],
        chroma: [
          { required: true, message: "色度不能为空", trigger: "blur" },
          { validator: this.validateNumber0d255, trigger: 'blur' }
        ],
        chnId: [
          { required: true, message: "拍摄通道不能为空", trigger: "blur" }
        ],
        resolution: [
          { required: true, message: "分辨率不能为空", trigger: "blur" }
        ],
        imgQuality: [
          { required: true, message: "图像质量不能为空", trigger: "blur" },
          { validator: this.validateNumber1d10, trigger: 'blur' }
        ]
      },
      // 文本信息下发表单校验
      textRules: {
        msgType: [
          { required: true, message: "信息标志不能为空", trigger: "blur" }
        ],
        text: [
          { required: true, message: "文本信息不能为空", trigger: "blur" }
        ]
      },
      // 设备参数设置表单校验
      paramRules: {
        locationStrategy: [
          { required: true, message: "位置汇报策略不能为空", trigger: "blur" }
        ],
        locationProject: [
          { required: true, message: "位置汇报方案不能为空", trigger: "blur" }
        ],
        heartInterval: [
          { required: true, message: "心跳发送间隔不能为空", trigger: "blur" }
        ],
        dormancyInterval: [
          { required: true, message: "休眠汇报间隔不能为空", trigger: "blur" }
        ],
        urgentWarningInterval: [
          { required: true, message: "紧急报警汇报间隔不能为空", trigger: "blur" }
        ],
        defaultInterval: [
          { required: true, message: "缺省汇报间隔不能为空", trigger: "blur" }
        ]
      },
      // 电话回拨表单校验
      phoneRules: {
        phoneCarType: [
          { required: true, message: "电话回拨标志不能为空", trigger: "blur" }
        ],
        phoneNo: [
          { required: true, message: "回拨电话号码不能为空", trigger: "blur" }
        ]
      },
      propParams: {
        id: null,
        yesNotCarDevNode: null
      },
      selectCarDevId: null,//选择的车辆设备id
      // 查询参数
      queryParams: {
        vehicleId: null
      },
      treeData: []
    }
  },
  watch: {
  },
  created() {
  },
  methods: {
    validateNumber1d10(rule, value, callback) {
      if (value < 1 || value > 10) {
        callback(new Error('数字必须在1到10之间'));
      } else {
        callback();
      }
    },
    validateNumber0d127(rule, value, callback) {
      if (value < 0 || value > 127) {
        callback(new Error('数字必须在0到127之间'));
      } else {
        callback();
      }
    },
    validateNumber0d255(rule, value, callback) {
      if (value < 0 || value > 255) {
        callback(new Error('数字必须在0到255之间'));
      } else {
        callback();
      }
    },
    loadNode(node, resolve) {
      if (node === null || node === undefined || node.level === 0){
        this.propParams.id = null;
        this.propParams.whetherCarDevNode = false;
        getCarDevTreeNoCHn(this.propParams).then(response => {
          this.treeData = response.data;
        });
      }else{
        if (node.data.isLeaf === true){
          return resolve([]);
        }
        this.propParams.id = node.data.id;
        this.propParams.whetherCarDevNode = node.data.whetherCarDevNode;
        this.propParams.whetherCompanyNode = node.data.whetherCompanyNode;

        getCarDevTreeNoCHn(this.propParams).then(response => {
          if(node.data.whetherCompanyNode){
            var label  = node.data.label;
            var label1 = label.split("(")[0];
            var label2 = response.data[0].onlineStatusCount;
            node.data.label = label1+label2;
          }
          return resolve(response.data);
        });
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if(this.queryParams.vehicleId === null || this.queryParams.vehicleId === ''|| this.queryParams.vehicleId.trim() === ''){
        this.loadNode();
      }else {
        getCarDevTreeNoCHnByVehicleId(this.queryParams).then(response => {
          this.treeData = response.data;
        });
      }
    },
    // 节点单击事件
    handleNodeClick(data) {
      const tree = this.$refs.myTree;
      if(data.whetherCarDevNode){
        if("01" === data.carDevStatus){
          this.selectCarDevId = data.id;
          if (tree) {
            tree.setCurrentKey(data.id);
          }
        }else {
          tree.setCurrentKey(null);
          this.$message({
            message: '该设备未在线',
            type: 'warning'
          });
          this.selectCarDevId = null;
        }
      }else {
        tree.setCurrentKey(null);
        this.selectCarDevId = null;
      }
    },
    /** 拍照提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (null != this.selectCarDevId && '' !== this.selectCarDevId) {
            this.form.selectCarDevId = this.selectCarDevId;
            this.loading = true;
            photoScreen(this.form).then(response => {
              this.$modal.msgSuccess("拍照成功");
              this.loading = false;
            }).catch(error => {
              this.loading = false;
              console.log(error);
            });
          }else {
            this.$message({
              message: '请选择车辆！',
              type: 'warning'
            });
            return false;
          }
        }
      });
    },
    /** 提交按钮 */
    textSubmitForm() {
      this.$refs["textForm"].validate(valid => {
        if (valid) {
          if (null != this.selectCarDevId && '' !== this.selectCarDevId) {
            this.textForm.selectCarDevId = this.selectCarDevId;
            this.loading = true;
            textSend(this.textForm).then(response => {
              this.$modal.msgSuccess("文本信息下发成功");
              this.loading = false;
            }).catch(error => {
              this.loading = false;
              console.log(error);
            });
          }else {
            this.$message({
              message: '请选择车辆！',
              type: 'warning'
            });
            return false;
          }
        }
      });
    },
    /** 参数查询按钮 */
    paramSelectSubmitForm() {
      if (null != this.selectCarDevId && '' !== this.selectCarDevId) {
        this.paramSelectForm.selectCarDevId = this.selectCarDevId;
        this.loading = true;
        paramSelect(this.paramSelectForm).then(response => {
          this.paramForm = response.data;
          this.$modal.msgSuccess("参数查询成功");
          this.loading = false;
        }).catch(error => {
          this.loading = false;
          console.log(error);
        });
      }else {
        this.$message({
          message: '请选择车辆！',
          type: 'warning'
        });
        return false;
      }
    },
    /** 参数设置按钮 */
    paramSubmitForm() {
      this.$refs["paramForm"].validate(valid => {
        if (valid) {
          if (null != this.selectCarDevId && '' !== this.selectCarDevId) {
            this.paramForm.selectCarDevId = this.selectCarDevId;
            this.loading = true;
            paramSet(this.paramForm).then(response => {
              this.$modal.msgSuccess("参数设置成功");
              this.loading = false;
            }).catch(error => {
              this.loading = false;
              console.log(error);
            });
          }else {
            this.$message({
              message: '请选择车辆！',
              type: 'warning'
            });
            return false;
          }
        }
      });
    },
    /** 提交按钮 */
    phoneSubmitForm() {
      this.$refs["phoneForm"].validate(valid => {
        if (valid) {
          if (null != this.selectCarDevId && '' !== this.selectCarDevId) {
            this.phoneForm.selectCarDevId = this.selectCarDevId;
            this.loading = true;
            phoneCar(this.phoneForm).then(response => {
              this.$modal.msgSuccess("电话回拨成功");
              this.loading = false;
            }).catch(error => {
              this.loading = false;
              console.log(error);
            });
          }else {
            this.$message({
              message: '请选择车辆！',
              type: 'warning'
            });
            return false;
          }
        }
      });
    },
  }
}
</script>

<style lang='scss' scoped>

.shade{
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.dialog{
  z-index: 891016; top: 45%; left: 50%;position:fixed;
}
.el-tabs--border-card{
  border-left: none;
}
.el-form-item{
  margin-bottom:35px;
}
.video-button{
  background-color: #e6a23c;
  color: white;
  height: 36px;
  margin-left: 10px
}
.video-select{
  width: 130px;
  margin-left:10px;
  font-size: 12px;
}
.video-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 84px);
}

.video-left-board {
  width: 260px;
  position: absolute;
  left: 0;
  top: 0;
  height: calc(100vh - 84px);
}

.video-left-board-top{
  background-color: #F5F7FA;
  position: relative;
  height: 40px;
  //background: #fff;
  border-bottom: 1px solid rgb(214, 214, 214);
  border-right: 1px solid rgb(214, 214, 214);
  box-sizing: border-box;
}

.video-left-board-down{
  border-right: 1px solid rgb(214, 214, 214);
  overflow: auto;
  position: relative;
  height: calc(100vh - 126px);
  background: #fff;
  box-sizing: border-box;
}
.video-center-board {
  height: calc(100vh - 84px);
  width: auto;
  margin: 0 0 0 260px;
  box-sizing: border-box;
}
.video-center-board-top{
  background-color: #E8EAED;
  position: relative;
  height: 42px;
  box-sizing: border-box;;
  border-top: none;
}
.video-center-board-down {
  overflow: hidden;
  position: relative;
  height: calc(100vh - 126px);
  background: #fff;
  box-sizing: border-box;
}

.video-right-board {
  width: 300px;
  position: absolute;
  right: 0;
  top: 0;
  height: calc(100vh - 84px);
}
</style>
