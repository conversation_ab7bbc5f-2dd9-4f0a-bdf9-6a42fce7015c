package com.gjxx.netty.req;


import com.gjxx.common.tcp.PackageData;

import java.util.Arrays;

/**
 * 终端通用应答
 */
public class TerminalCommonMsg extends PackageData {

	private TerminalCommonInfo terminalCommonInfo;

	public TerminalCommonMsg() {
	}

	public TerminalCommonMsg(PackageData packageData) {
		this();
		this.channel = packageData.getChannel();
		this.checkSum = packageData.getCheckSum();
		this.calculatedCheckSum = packageData.getCalculatedCheckSum();
		this.msgBodyBytes = packageData.getMsgBodyBytes();
		this.msgHeader = packageData.getMsgHeader();
	}

	public TerminalCommonInfo getTerminalCommonInfo() {
		return terminalCommonInfo;
	}

	public void setTerminalCommonInfo(TerminalCommonInfo msgBody) {
		this.terminalCommonInfo = msgBody;
	}

	@Override
	public String toString() {
		return "TerminalCommonMsg [terminalCommonInfo=" + terminalCommonInfo + ", msgHeader=" + msgHeader
				+ ", msgBodyBytes=" + Arrays.toString(msgBodyBytes) + ", checkSum=" + checkSum
				+", calculatedCheckSum=" + calculatedCheckSum + ", channel=" + channel
				+ "]";
	}

	public static class TerminalCommonInfo {
		// 应答流水号(WORD)
		private int respflowId;
		// 应答ID(WORD)
		private int respId;
		// 结果(BYTE) 0：成功/确认；1：失败；2：消息有误；3：不支持
		private int result;

		public TerminalCommonInfo() {
		}
		public int getRespflowId() {
			return respflowId;
		}

		public void setRespflowId(int respflowId) {
			this.respflowId = respflowId;
		}

		public int getRespId() {
			return respId;
		}

		public void setRespId(int respId) {
			this.respId = respId;
		}

		public int getResult() {
			return result;
		}

		public void setResult(int result) {
			this.result = result;
		}

		@Override
		public String toString() {
			return "TerminalCommonInfo [respflowId=" + respflowId + ", respId=" + respId + ", result="
					+ result + "]";
		}

	}
}
