package com.gjxx.system.service.impl;

import com.gjxx.BaseServiceTest;
import com.gjxx.system.domain.SysLogininfor;
import com.gjxx.system.service.ISysLogininforService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SysLogininforServiceImplTest extends BaseServiceTest {

    @Autowired
    private ISysLogininforService logininforService;

    @Test
    void getById() {
        SysLogininfor byId = logininforService.getById(1L);
        System.out.println("byId = " + byId);
    }
}
