package ${packageName}.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.gjxx.common.core.domain.R;
import com.gjxx.common.utils.old.ConvertUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.gjxx.common.annotation.Log;
import com.gjxx.common.core.controller.BaseController;
import com.gjxx.common.enums.BusinessType;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import com.gjxx.common.utils.poi.ExcelUtil;
#if($table.crud || $table.sub)
import com.gjxx.common.core.page.TableDataInfo;
#elseif($table.tree)
#end

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
##@Api(tags = "${functionName}")
@RestController
@RequestMapping("/${moduleName}/${businessName}")
public class ${ClassName}Controller extends BaseController {
    @Autowired
    private I${ClassName}Service ${className}Service;

    /**
     * 查询${functionName}列表
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:list')")
##    @ApiOperation(value = "查询${functionName}列表", notes = "查询${functionName}列表")
    @GetMapping("/list")
#if($table.crud || $table.sub)
    public TableDataInfo list(${ClassName} ${className}) {
        startPage();
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return getDataTable(list);
    }
#elseif($table.tree)
    public AjaxResult list(${ClassName} ${className}) {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return success(list);
    }
#end

    /**
     * 导出${functionName}列表
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:export')")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ${ClassName} ${className}) {
        ExcelUtil<${ClassName}> util = new ExcelUtil<>(${ClassName}.class);

        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        util.exportExcel(response, list, "${functionName}数据");
    }

    /**
     * 获取${functionName}详细信息
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:query')")
##    @ApiOperation(value = "获取${functionName}详细信息", notes = "获取${functionName}详细信息")
    @GetMapping(value = "/{${pkColumn.javaField}}")
    public R<${ClassName}> getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return R.ok(${className}Service.getById(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:add')")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
##    @ApiOperation(value = "新增${functionName}", notes = "新增${functionName}")
    @PostMapping
    public R<Boolean> add(@RequestBody ${ClassName} ${className}) {
        return R.ok(${className}Service.save(${className}));
    }

    /**
     * 修改${functionName}
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:edit')")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
##    @ApiOperation(value = "修改${functionName}", notes = "修改${functionName}")
    @PutMapping
    public R<Boolean> edit(@RequestBody ${ClassName} ${className}) {
        return R.ok(${className}Service.updateById(${className}));
    }

    /**
     * 删除${functionName}
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:remove')")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
##    @ApiOperation(value = "删除${functionName}", notes = "删除${functionName}")
	@DeleteMapping("/{${pkColumn.javaField}s}")
    public R<Boolean> remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return R.ok(${className}Service.removeBatchByIds(CollectionUtils.arrayToList(${pkColumn.javaField}s)));
    }
}
