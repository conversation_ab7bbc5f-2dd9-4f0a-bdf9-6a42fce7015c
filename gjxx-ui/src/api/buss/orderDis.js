import request from '@/utils/request'

// 发送设备拍照命令
export function photoScreen(query) {
  return request({
    url: '/buss/carDev/photoScreen',
    method: 'get',
    params: query
  })
}

// 文本信息下发命令
export function textSend(query) {
  return request({
    url: '/buss/carDev/textSend',
    method: 'get',
    params: query
  })
}

// 设备参数查询命令
export function paramSelect(query) {
  return request({
    url: '/buss/carDev/paramSelect',
    method: 'get',
    params: query
  })
}

// 设备参数设置命令
export function paramSet(query) {
  return request({
    url: '/buss/carDev/paramSet',
    method: 'get',
    params: query
  })
}

// 电话回拨命令
export function phoneCar(query) {
  return request({
    url: '/buss/carDev/phoneCar',
    method: 'get',
    params: query
  })
}

