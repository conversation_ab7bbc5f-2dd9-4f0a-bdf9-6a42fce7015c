package com.gjxx.common.utils;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

public class StringToByteBufConverter {
    public static ByteBuf stringToByteBuf(String str) {
        // 将字符串转换为字节
        byte[] bytes = str.getBytes();

        // 创建一个包含给定字节的ByteBuf
        ByteBuf byteBuf = Unpooled.buffer(bytes.length);
        byteBuf.writeBytes(bytes);

        return byteBuf;
    }

    public static void main(String[] args) {
        String str = "Hello, Netty!";
        ByteBuf byteBuf = stringToByteBuf(str);

        // 打印ByteBuf中的内容
        for (int i = 0; i < byteBuf.capacity(); i++) {
            System.out.print((char) byteBuf.getByte(i));
        }
    }
}
