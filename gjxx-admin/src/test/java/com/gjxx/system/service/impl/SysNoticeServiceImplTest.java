package com.gjxx.system.service.impl;

import com.gjxx.BaseServiceTest;
import com.gjxx.system.domain.SysNotice;
import com.gjxx.system.service.ISysNoticeService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SysNoticeServiceImplTest extends BaseServiceTest {

    @Autowired
    private ISysNoticeService sysNoticeService;

    @Test
    void getById() {
        SysNotice byId = sysNoticeService.getById(1L);
        System.out.println("byId = " + byId);
    }

    @Test
    void selectNoticeById() {
        SysNotice sysNotice = this.sysNoticeService.selectNoticeById(1L);
        System.out.println("sysNotice = " + sysNotice);
    }
}
