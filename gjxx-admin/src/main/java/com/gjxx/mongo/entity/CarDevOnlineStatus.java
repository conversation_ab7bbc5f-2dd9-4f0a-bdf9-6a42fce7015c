package com.gjxx.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "carDevOnlineStatus")  //类似于@Table标签，用于指明数据库表名/集合名
public class CarDevOnlineStatus {

    @Id
    /** SIM卡号 */
    private String id;
    /** 车辆标识 */
    private String vehicleId;
    /** 视频通道 */
    private String vedioCh;
    /** 隶属车队ID */
    private Long carTeamId;
    /** 车队名称 */
    private String carTeamName;
    /** ACC点火开关状态 01-开，02-关 */
    private String accStatus;
    /** 报警状态 01-报警，02-未报警 */
    private String warningStatus;
    /** 最后定位经度 */
    private String longitude;
    /** 最后定位纬度 */
    private String latitude;
    // 方向 0-359，正北为 0，顺时针
    private int direction;
    /** 最后定位时间 */
    private String posTime;
}

