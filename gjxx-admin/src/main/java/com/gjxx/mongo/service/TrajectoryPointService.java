package com.gjxx.mongo.service;

import com.gjxx.buss.domain.bean.TraceVo;
import com.gjxx.mongo.entity.TrajectoryPoint;

import java.util.List;

public interface TrajectoryPointService {
    //定义一个查询方法
    TrajectoryPoint findTrajectoryPointById(String id);
    //定义一个保存方法
    void save(TrajectoryPoint trajectoryPoint);
    //定义一个删除方法
    void delete(String id);
    //定义一个根据时间查询方法
    List<TrajectoryPoint> findAllByTime(String startTime, String endTime);
}

