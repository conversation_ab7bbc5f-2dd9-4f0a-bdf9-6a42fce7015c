package com.gjxx.common.utils;


import com.gjxx.common.annotation.EnumField;
import com.gjxx.common.enums.IEnum;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

/**
 * 枚举工具类
 *
 * <AUTHOR>
 * @date 2018/10/22
 */
public class BaseEnumUtil {

	
	/**
	 * 依据IEnum注解 获取枚举类序列
	 * 
	 * @param enumClazz
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public static List<Map<String, Object>> getSerializes(Class<? extends IEnum> enumClazz) {

		List<Map<String, Object>> results = new ArrayList<>();

		Method[] methods = IEnum.class.getDeclaredMethods();

		List<?> list = Arrays.asList(enumClazz.getEnumConstants());

		Map<String,Object> map ;
		
		for (Object clazz : list) {

			map = new HashMap<>(16);
			
			for (Method method : methods) {
				EnumField enumField = method.getAnnotation(EnumField.class);
				String fieldName = enumField.value();
				try {
					map.put(fieldName, method.invoke(clazz));
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				} catch (IllegalArgumentException e) {
					e.printStackTrace();
				} catch (InvocationTargetException e) {
					e.printStackTrace();
				}
			}
			results.add(map);
		}
		return results;

	}

}
