package com.gjxx.common.utils;

import java.text.*;
import java.util.Calendar;

/**
 * @ Description：流水号
 */
public class GenerateSequenceUtil {

	private static final FieldPosition HELPER_POSITION = new FieldPosition(0);

	private final static Format dateFormat = new SimpleDateFormat("MMddHHmmss");

	private final static NumberFormat numberFormat = new DecimalFormat("00000000");

	private static int seq = 1;

	private static final int MAX = 99999999;


	private final static NumberFormat nine_numberFormat = new DecimalFormat("000000000");

	private static int nine_seq = 1;

	private static final int nine_MAX = 999999999;

	/**
	* 时间格式生成序列
	* @return String
	*/
	public static synchronized String generateSequenceNo() {
		Calendar rightNow = Calendar.getInstance();
		StringBuffer sb = new StringBuffer();
		dateFormat.format(rightNow.getTime(), sb, HELPER_POSITION);
		numberFormat.format(seq, sb, HELPER_POSITION);
		if (seq == MAX) {
			seq = 1;
		} else {
			seq++;
		}
		return sb.toString();
	}

	/**
	 * 时间格式生成序列9位
	 * @return String
	 */
	public static synchronized String generateSequenceNine(){
		StringBuffer sb = new StringBuffer();
		nine_numberFormat.format(nine_seq, sb, HELPER_POSITION);
		if (nine_seq == nine_MAX) {
			nine_seq = 1;
		} else {
			nine_seq++;
		}
		return sb.toString();
	}

	public static void main(String[] args) {
		String ss = generateSequenceNo();
		System.out.println(ss);
		String str = generateSequenceNine();
		System.out.println(str);
	}
}
