package com.gjxx.system.service;

import com.gjxx.system.domain.vo.FileVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传接口
 *
 * <AUTHOR>
 */
public interface ISysFileService {
    /**
     * 文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址uri
     * @throws Exception
     */
    String uploadFile(MultipartFile file,String stuNum) throws Exception;

    /**
     * 文件上传接口(全路径)
     * @param file
     * @return
     * @throws Exception
     */
    FileVo uploadFileNew(MultipartFile file,String stuNum) throws Exception;
    /**
     * 文件上传接口
     *
     * @param fileBytes 上传的文件
     * @return 访问地址uri
     * @throws Exception
     */
    String uploadFile(byte[] fileBytes,String fileNameHead) throws Exception;
}
