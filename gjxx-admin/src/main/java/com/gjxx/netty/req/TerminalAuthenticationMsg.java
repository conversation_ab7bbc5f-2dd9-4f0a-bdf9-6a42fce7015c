package com.gjxx.netty.req;


import com.gjxx.common.tcp.PackageData;
import com.gjxx.netty.codec.TPMSConsts;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;

/**
 * 终端鉴权消息
 *
 * <AUTHOR>
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TerminalAuthenticationMsg extends PackageData {
	private String authCode;

	public TerminalAuthenticationMsg() {
	}

	public TerminalAuthenticationMsg(PackageData packageData) {
		this();
		this.channel = packageData.getChannel();
		this.checkSum = packageData.getCheckSum();
		this.calculatedCheckSum = packageData.getCalculatedCheckSum();
		this.msgBodyBytes = packageData.getMsgBodyBytes();
		this.msgHeader = packageData.getMsgHeader();
		this.authCode = new String(packageData.getMsgBodyBytes(), TPMSConsts.string_charset);
	}
}
