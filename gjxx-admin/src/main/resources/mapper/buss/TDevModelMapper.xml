<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gjxx.buss.mapper.TDevModelMapper">


    <select id="selectTDevModelList" parameterType="com.gjxx.buss.domain.TDevModel" resultType="com.gjxx.buss.domain.TDevModel">
        select * from t_dev_model
        <where>
            <if test="devModel != null  and devModel != ''">
                and dev_model like concat('%', #{devModel}, '%')
            </if>
            <if test="supplierName != null  and supplierName != ''">
                and supplier_name like concat('%', #{supplierName}, '%')
            </if>
            <if test="contactPerson != null  and contactPerson != ''">
                and contact_person = #{contactPerson}
            </if>
            <if test="contactPhone != null  and contactPhone != ''">
                and contact_phone = #{contactPhone}
            </if>
            <if test="contactAddress != null  and contactAddress != ''">
                and contact_address = #{contactAddress}
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
            <if test="createUser != null  and createUser != ''">
                and create_user = #{createUser}
            </if>
            <if test="updateUser != null  and updateUser != ''">
                and update_user = #{updateUser}
            </if>
        </where>
    </select>
</mapper>
