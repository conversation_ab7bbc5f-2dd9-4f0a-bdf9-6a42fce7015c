package com.gjxx.common.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonView;
import com.gjxx.common.enums.ErrorCodeType;
import com.gjxx.common.exception.MsgNotifyException;
import com.gjxx.common.exception.MsgPassException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResultEntity implements Serializable {

    private static final long serialVersionUID = 8092549684746600012L;
    /**
     * 日志
     */
    private static Logger _logger = LoggerFactory.getLogger(ResultEntity.class);

    /**
     * 定义结果视图接口
     */
    public interface ResultView {
    }

    @JsonView(ResultView.class)
    private Integer errorcode;

    @JsonView(ResultView.class)
    private String message;

    @JsonView(ResultView.class)
    private Object data;

    public ResultEntity() {
        errorcode = ErrorCodeType.SUCCESS.getValue();
        message = "操作成功";
        data = null;
    }

    /**
     * @param errorcode
     * @param message
     * @param data
     */
    public ResultEntity(ErrorCodeType errorcode, String message, Object data) {
        super();
        this.errorcode = errorcode.getValue();
        this.message = message;
        this.data = data;
    }

    public void setErrorInfo(ErrorCodeType err, String msg, Object data) {
        this.errorcode = err.getValue();
        this.message = msg;
        this.data = data;
    }

    /**
     * 设置P_FAILURE 信息
     *
     * @param msg
     */
    public void setFailureMsg(String msg) {
        this.errorcode = ErrorCodeType.P_FAILURE.getValue();
        this.message = msg;
    }

    /**
     * 设置errorcode 和 msg
     *
     * @param msg
     * @param errorcode
     */
    public void setCodeMsg(String msg, Integer errorcode) {
        this.errorcode = errorcode;
        this.message = msg;
    }

    /**
     * 根据异常信息设置返回
     *
     * @param e
     */
    public void setFailure(Exception e) {

        String msg = ErrorCodeType.P_FAILURE.getMsg();
        Integer errorcode = ErrorCodeType.P_FAILURE.getValue();

        Class<? extends Exception> eClass = e.getClass();

        if (MsgNotifyException.class.isAssignableFrom(eClass)) {
            msg = e.getMessage();
            errorcode = ((MsgNotifyException) e).getErrorcode();
            _logger.info(msg, e);
        } else if (MsgPassException.class.isAssignableFrom(eClass)) {
            _logger.info(e.getMessage(), e);
        } else {
            _logger.error("系统错误", e);
        }

        setCodeMsg(msg, errorcode);

    }

    public Integer getErrorcode() {
        return errorcode;
    }

    public void setErrorcode(Integer errorcode) {
        this.errorcode = errorcode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }


    /**
     * 请求是否成功
     *
     * @return
     */
    @JsonIgnore
    public Boolean isSuccess() {
        if (ErrorCodeType.SUCCESS.getValue().equals(getErrorcode())) {
            return true;
        } else {
            return false;
        }
    }

}
