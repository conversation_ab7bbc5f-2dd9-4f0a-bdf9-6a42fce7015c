package com.gjxx.buss.domain.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.gjxx.buss.domain.TCarTeam;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * BussTreeSelect树结构实体类
 *
 * <AUTHOR>
 */
public class BussTreeSelect implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 子节点
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<BussTreeSelect> children;

    public BussTreeSelect() {

    }

    public BussTreeSelect(TCarTeam tCarTeam) {
        this.id = tCarTeam.getId();
        this.label = tCarTeam.getNameAlias();
        this.children = tCarTeam.getChildren().stream().map(BussTreeSelect::new).collect(Collectors.toList());
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<BussTreeSelect> getChildren() {
        return children;
    }

    public void setChildren(List<BussTreeSelect> children) {
        this.children = children;
    }
}
