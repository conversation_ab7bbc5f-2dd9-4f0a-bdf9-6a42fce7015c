package com.gjxx.buss.service;

import com.gjxx.buss.domain.bean.BussTreeSelect;
import com.gjxx.common.mp.service.MpService;
import java.util.List;
import com.gjxx.buss.domain.TCarTeam;

/**
 * 车队信息Service接口
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface ITCarTeamService extends MpService<TCarTeam> {

    /**
     * 查询车队信息列表
     *
     * @param tCarTeam 车队信息
     * @return 车队信息集合
     */
    List<TCarTeam> selectTCarTeamList(TCarTeam tCarTeam);

    /**
     * 查询车队树结构信息
     *
     * @param tCarTeam 车队信息
     * @return 车队树信息集合
     */
    List<BussTreeSelect> selectCarTeamTreeList(TCarTeam tCarTeam);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param tCarTeams 车队列表
     * @return 下拉树结构列表
     */
    List<BussTreeSelect> buildCarTeamBussTreeSelect(List<TCarTeam> tCarTeams);
    /**
     * 构建前端所需要树结构
     *
     * @param tCarTeams 车队列表
     * @return 树结构列表
     */
    List<TCarTeam> buildCarTeamTree(List<TCarTeam> tCarTeams);
    /**
     * 根据车队ID递归查询车队ID
     * @param carTeamId
     * @return
     */
    List<Long> listChildCarTeamByCarTeamId(Long carTeamId);
}
