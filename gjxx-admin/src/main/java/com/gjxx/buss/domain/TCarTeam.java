package com.gjxx.buss.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.gjxx.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gjxx.common.core.domain.MpBaseEntity;
import com.gjxx.common.core.domain.entity.SysDept;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 车队信息对象 t_car_team
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName("t_car_team")
@ApiModel(value = "车队信息")
public class TCarTeam extends MpBaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty("ID")
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 车队名称 */
    @Excel(name = "车队名称")
    @ApiModelProperty("车队名称")
    private String name;

    /** 车队简称 */
    @Excel(name = "车队简称")
    @ApiModelProperty("车队简称")
    private String nameAlias;

    /** 父级ID */
    @Excel(name = "父级ID")
    @ApiModelProperty("父级ID")
    private Long parentId;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    @ApiModelProperty("显示顺序")
    private Integer orderNum;

    /** 是否为公司 */
    @Excel(name = "是否为公司")
    @ApiModelProperty("是否为公司")
    private String whetherCompany;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private String createUser;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /** 修改人 */
    @Excel(name = "修改人")
    @ApiModelProperty("修改人")
    private String updateUser;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 子车队
     */
    @TableField(exist = false)
    private List<TCarTeam> children = new ArrayList<>();
}
