import {getEnums} from "@/api/system/config";

const getDefaultState = () => {
  return {
    enums: {},
  }
}

const state = getDefaultState()

const mutations = {
  SET_ENUMS: (state, enums) => {
    state.enums = enums;
  }
}

const actions = {
  getEnums({commit}) {
    return new Promise((resolve, reject) => {
      getEnums().then(response => {
        const {
          data
        } = response
        commit('SET_ENUMS', data)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true, state, mutations, actions
}
