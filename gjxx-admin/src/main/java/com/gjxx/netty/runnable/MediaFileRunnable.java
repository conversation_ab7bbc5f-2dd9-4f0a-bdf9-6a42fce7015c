package com.gjxx.netty.runnable;

import com.alibaba.fastjson2.JSON;
import com.gjxx.buss.domain.TMediaFile;
import com.gjxx.buss.service.ITMediaFileService;
import com.gjxx.common.tcp.MediaFileMsg;
import com.gjxx.common.utils.SpringBeanUtil;
import com.gjxx.system.service.ISysFileService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;

import java.io.IOException;


/**
 */
@Slf4j
public class MediaFileRunnable implements Runnable {
    /**
     * 消息文本
     */
    private String message;

    private ITMediaFileService mediaFileService;
    private ISysFileService sysFileService;

    public MediaFileRunnable(String message) {
        this.message = message;
        this.mediaFileService = SpringBeanUtil.getBean(ITMediaFileService.class);
        this.sysFileService = SpringBeanUtil.getBean(ISysFileService.class);
    }

    @Override
    public void run() {
        MediaFileMsg mediaFileMsg = JSON.parseObject(message, MediaFileMsg.class);
        TMediaFile tMediaFile = new TMediaFile();
        tMediaFile.setSimNo(mediaFileMsg.getTerminalPhone());
        tMediaFile.setFileType(String.valueOf(mediaFileMsg.getMediaType()));
        tMediaFile.setFormatCode(String.valueOf(mediaFileMsg.getFormatCode()));
        tMediaFile.setEventCode(String.valueOf(mediaFileMsg.getEventCode()));
        tMediaFile.setVedioCh(String.valueOf(mediaFileMsg.getChnId()));
        try {
            String filePath = sysFileService.uploadFile(mediaFileMsg.getFile(),"mediaFile");
            tMediaFile.setFilePath(filePath);
            mediaFileService.save(tMediaFile);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("[多媒体文件消息队列消费] minio保存文件失败:{}",e.getMessage());
        }
    }
}
