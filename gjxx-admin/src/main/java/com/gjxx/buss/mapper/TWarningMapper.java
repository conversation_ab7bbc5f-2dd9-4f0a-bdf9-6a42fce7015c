package com.gjxx.buss.mapper;

import com.gjxx.common.mp.mapper.MpBaseMapper;
import java.util.List;
import java.util.Map;
import com.gjxx.buss.domain.TWarning;
import org.apache.ibatis.annotations.Param;

/**
 * 报警信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface TWarningMapper extends MpBaseMapper<TWarning> {


    /**
     * 查询报警信息列表
     *
     * @param tWarning 报警信息
     * @return 报警信息集合
     */
    List<TWarning> selectTWarningList(TWarning tWarning);

    /**
     * 批量处理报警信息
     */
    int handleBatchByIds(@Param("ids") Long[] ids,@Param("userName")  String userName);

    /**
     * 批量处理报警信息
     */
    int handleNoByIds(@Param("ids") Long[] ids,@Param("userName") String userName);
}
