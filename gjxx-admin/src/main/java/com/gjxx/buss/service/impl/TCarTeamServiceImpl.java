package com.gjxx.buss.service.impl;

import com.gjxx.buss.domain.bean.BussTreeSelect;
import com.gjxx.common.mp.service.impl.MpServiceImpl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import com.gjxx.common.utils.StringUtils;
import com.gjxx.common.utils.spring.SpringUtils;
import org.springframework.stereotype.Service;
import com.gjxx.buss.mapper.TCarTeamMapper;
import com.gjxx.buss.domain.TCarTeam;
import com.gjxx.buss.service.ITCarTeamService;

/**
 * 车队信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class TCarTeamServiceImpl extends MpServiceImpl<TCarTeamMapper, TCarTeam> implements ITCarTeamService {

    /**
     * 查询车队信息列表
     *
     * @param tCarTeam 车队信息
     * @return 车队信息
     */
    @Override
    public List<TCarTeam> selectTCarTeamList(TCarTeam tCarTeam) {
        return baseMapper.selectTCarTeamList(tCarTeam);
    }

    @Override
    public List<BussTreeSelect> selectCarTeamTreeList(TCarTeam tCarTeam) {
        List<TCarTeam> tCarTeams = SpringUtils.getAopProxy(this).selectTCarTeamList(tCarTeam);
        return buildCarTeamBussTreeSelect(tCarTeams);
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param tCarTeams 车队列表
     * @return 下拉树结构列表
     */
    @Override
    public List<BussTreeSelect> buildCarTeamBussTreeSelect(List<TCarTeam> tCarTeams) {
        List<TCarTeam> carTeamTrees = buildCarTeamTree(tCarTeams);
        return carTeamTrees.stream().map(BussTreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 构建前端所需要树结构
     *
     * @param tCarTeams 车队列表
     * @return 树结构列表
     */
    @Override
    public List<TCarTeam> buildCarTeamTree(List<TCarTeam> tCarTeams) {
        List<TCarTeam> returnList = new ArrayList<TCarTeam>();
        List<Long> tempList = tCarTeams.stream().map(TCarTeam::getId).collect(Collectors.toList());
        for (TCarTeam tCarTeam : tCarTeams) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(tCarTeam.getParentId())) {
                recursionFn(tCarTeams, tCarTeam);
                returnList.add(tCarTeam);
            }
        }
        if (returnList.isEmpty()) {
            returnList = tCarTeams;
        }
        return returnList;
    }

    @Override
    public List<Long> listChildCarTeamByCarTeamId(Long carTeamId) {
        return baseMapper.listChildCarTeamByCarTeamId(carTeamId);
    }


    /**
     * 递归列表
     */
    private void recursionFn(List<TCarTeam> list, TCarTeam t) {
        // 得到子节点列表
        List<TCarTeam> childList = getChildList(list, t);
        t.setChildren(childList);
        for (TCarTeam tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<TCarTeam> getChildList(List<TCarTeam> list, TCarTeam t) {
        List<TCarTeam> tlist = new ArrayList<>();
        Iterator<TCarTeam> it = list.iterator();
        while (it.hasNext()) {
            TCarTeam n = it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<TCarTeam> list, TCarTeam t) {
        return getChildList(list, t).size() > 0;
    }

}
