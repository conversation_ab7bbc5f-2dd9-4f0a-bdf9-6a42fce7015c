package com.gjxx.common.exception;


import com.gjxx.common.enums.ErrorCodeType;

/**
 * <AUTHOR>
 * @date 2020/8/18
 * 消息通知异常
 */
public class MsgNotifyException extends RuntimeException {

    private static final long serialVersionUID = -2274188427001251519L;

    protected final String message;

    protected final Integer errorcode;

    public MsgNotifyException(String message) {
        super(message);
        this.message = message;
        this.errorcode = ErrorCodeType.P_FAILURE.getValue();
    }


    public MsgNotifyException(ErrorCodeType errorCodeType) {
        super(errorCodeType.getMsg());
        this.message = errorCodeType.getMsg();
        this.errorcode = errorCodeType.getValue();
    }

    @Override
    public String getMessage() {
        return message;
    }

    public Integer getErrorcode() {
        return errorcode;
    }
}
