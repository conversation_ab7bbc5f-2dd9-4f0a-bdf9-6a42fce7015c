package com.gjxx.buss.mapper;

import com.gjxx.common.mp.mapper.MpBaseMapper;
import java.util.List;
import java.util.Map;
import com.gjxx.buss.domain.TCarTeam;
import org.apache.ibatis.annotations.Param;

/**
 * 车队信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface TCarTeamMapper extends MpBaseMapper<TCarTeam> {


    /**
     * 查询车队信息列表
     *
     * @param tCarTeam 车队信息
     * @return 车队信息集合
     */
    List<TCarTeam> selectTCarTeamList(TCarTeam tCarTeam);
    /**
     * 根据车队ID递归查询车队ID
     * @param carTeamId
     * @return
     */
    List<Long> listChildCarTeamByCarTeamId(@Param("carTeamId") Long carTeamId);
}
