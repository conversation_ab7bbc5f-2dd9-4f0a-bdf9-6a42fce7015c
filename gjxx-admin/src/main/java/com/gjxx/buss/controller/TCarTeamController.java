package com.gjxx.buss.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gjxx.buss.domain.TCarDev;
import com.gjxx.buss.domain.TDevModel;
import com.gjxx.buss.service.ITCarDevService;
import com.gjxx.common.core.domain.AjaxResult;
import com.gjxx.common.core.domain.R;
import com.gjxx.common.core.domain.entity.SysDept;
import com.gjxx.common.core.domain.entity.SysUser;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.gjxx.common.annotation.Log;
import com.gjxx.common.core.controller.BaseController;
import com.gjxx.common.enums.BusinessType;
import com.gjxx.buss.domain.TCarTeam;
import com.gjxx.buss.service.ITCarTeamService;
import com.gjxx.common.utils.poi.ExcelUtil;
import com.gjxx.common.core.page.TableDataInfo;

/**
 * 车队信息Controller
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/buss/carTeam")
public class TCarTeamController extends BaseController {
    @Autowired
    private ITCarTeamService tCarTeamService;
    @Autowired
    private ITCarDevService tCarDevService;

    /**
     * 查询车队信息列表
     */
    @PreAuthorize("@ss.hasPermi('buss:carTeam:list')")
    @GetMapping("/list")
    public AjaxResult list(TCarTeam tCarTeam) {
        startPage();
        List<TCarTeam> list = tCarTeamService.selectTCarTeamList(tCarTeam);
        return success(list);
    }

    /**
     * 导出车队信息列表
     */
    @PreAuthorize("@ss.hasPermi('buss:carTeam:export')")
    @Log(title = "车队信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TCarTeam tCarTeam) {
        ExcelUtil<TCarTeam> util = new ExcelUtil<>(TCarTeam.class);

        List<TCarTeam> list = tCarTeamService.selectTCarTeamList(tCarTeam);
        util.exportExcel(response, list, "车队信息数据");
    }

    /**
     * 获取车队信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('buss:carTeam:query')")
    @GetMapping(value = "/{id}")
    public R<TCarTeam> getInfo(@PathVariable("id") Long id) {
        return R.ok(tCarTeamService.getById(id));
    }

    /**
     * 新增车队信息
     */
    @PreAuthorize("@ss.hasPermi('buss:carTeam:add')")
    @Log(title = "车队信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody TCarTeam tCarTeam) {
        long carTeamNameCount = tCarTeamService.count(new QueryWrapper<TCarTeam>().eq("name",tCarTeam.getName()));
        if(carTeamNameCount > 0){
            return R.fail("车队名称已存在，请重新输入！");
        }
        long carTeamAliasCount = tCarTeamService.count(new QueryWrapper<TCarTeam>().eq("name_alias",tCarTeam.getNameAlias()));
        if(carTeamAliasCount > 0){
            return R.fail("车队简称已存在，请重新输入！");
        }
        tCarTeam.setCreateUser(getUsername());
        tCarTeam.setUpdateUser(getUsername());
        return R.ok(tCarTeamService.save(tCarTeam));
    }

    /**
     * 修改车队信息
     */
    @PreAuthorize("@ss.hasPermi('buss:carTeam:edit')")
    @Log(title = "车队信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody TCarTeam tCarTeam) {
        List<TCarTeam> tCarTeamList = tCarTeamService.list(new QueryWrapper<TCarTeam>().eq("name", tCarTeam.getName()));
        if(tCarTeamList.size() > 0){
            if(!tCarTeamList.get(0).getId().equals(tCarTeam.getId())){
                return R.fail("车队名称已存在，请重新输入！");
            }
        }
        List<TCarTeam> tCarTeamList2 = tCarTeamService.list(new QueryWrapper<TCarTeam>().eq("name_alias", tCarTeam.getNameAlias()));
        if(tCarTeamList2.size() > 0){
            if(!tCarTeamList2.get(0).getId().equals(tCarTeam.getId())){
                return R.fail("车队简称已存在，请重新输入！");
            }
        }
        tCarTeam.setUpdateUser(getUsername());
        return R.ok(tCarTeamService.updateById(tCarTeam));
    }

    /**
     * 删除车队信息
     */
    @PreAuthorize("@ss.hasPermi('buss:carTeam:remove')")
    @Log(title = "车队信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        for(Long id : ids){
            long carTeamCount = tCarTeamService.count(new QueryWrapper<TCarTeam>().eq("parent_id",id));
            if(carTeamCount > 0){
                return R.fail("有子集车队存在，不能删除！");
            }
            long carDevCount = tCarDevService.count(new QueryWrapper<TCarDev>().eq("car_team_id",id));
            if(carDevCount > 0){
                return R.fail("车队下存在终端设备，不能删除！");
            }
        }
        return R.ok(tCarTeamService.removeBatchByIds(CollectionUtils.arrayToList(ids)));
    }

    /**
     * 获取车队树列表
     */
    @PreAuthorize("@ss.hasPermi('buss:carTeam:carTeamTree')")
    @GetMapping("/carTeamTree")
    public AjaxResult carTeamTree(TCarTeam tCarTeam) {
        return success(tCarTeamService.selectCarTeamTreeList(tCarTeam));
    }

    /**
     * 查询车队map
     */
    @PreAuthorize("@ss.hasPermi('buss:carTeam:getCarTeamMap')")
    @GetMapping(value ="/getCarTeamMap")
    public AjaxResult getCarTeamMap() {
        AjaxResult ajax = AjaxResult.success();
        Map<Long, String> carTeamMap = new HashMap<>();
        List<TCarTeam> carTeams = tCarTeamService.list();
        for(TCarTeam carTeam : carTeams){
            carTeamMap.put(carTeam.getId(),carTeam.getNameAlias());
        }
        ajax.put("carTeamMap", carTeamMap);
        return ajax;
    }
}
