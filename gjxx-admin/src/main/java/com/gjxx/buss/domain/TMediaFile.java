package com.gjxx.buss.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.gjxx.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gjxx.common.core.domain.MpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 多媒体文件信息对象 t_media_file
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName("t_media_file")
@ApiModel(value = "多媒体文件信息")
public class TMediaFile extends MpBaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty("ID")
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** SIM卡号 */
    @Excel(name = "SIM卡号")
    @ApiModelProperty("SIM卡号")
    private String simNo;

    /** 多媒体类型 */
    @Excel(name = "多媒体类型")
    @ApiModelProperty("多媒体类型")
    private String fileType;

    /** 格式编码 */
    @Excel(name = "格式编码")
    @ApiModelProperty("格式编码")
    private String formatCode;

    /** 事件编码 */
    @Excel(name = "事件编码")
    @ApiModelProperty("事件编码")
    private String eventCode;

    /** 文件路径 */
    @Excel(name = "文件路径")
    @ApiModelProperty("文件路径")
    private String filePath;

    /** 视频通道 */
    @Excel(name = "视频通道")
    @ApiModelProperty("视频通道")
    private String vedioCh;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /** 车辆设备ID */
    @ApiModelProperty("车辆设备ID")
    @TableField(exist = false)
    private String selectCarDevId;

    /** 车辆标识 */
    @ApiModelProperty("车辆标识")
    @TableField(exist = false)
    private String vehicleId;
}
