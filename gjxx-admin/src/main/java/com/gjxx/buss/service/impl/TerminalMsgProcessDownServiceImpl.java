package com.gjxx.buss.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.gjxx.buss.domain.bean.*;
import com.gjxx.buss.service.BaseMsgProcessService;
import com.gjxx.buss.service.TerminalMsgProcessDownService;
import com.gjxx.common.config.VideoPlayConfig;
import com.gjxx.common.constant.CacheConstants;
import com.gjxx.common.core.domain.R;
import com.gjxx.common.core.redis.RedisCache;
import com.gjxx.common.tcp.HistoryVideoMsg;
import com.gjxx.common.tcp.ParamQueryMsg;
import com.gjxx.mongo.service.CarDevOnlineStatusService;
import com.gjxx.netty.MyEncoder;
import com.gjxx.netty.codec.BCD8421Operater;
import com.gjxx.netty.codec.BitOperator;
import com.gjxx.netty.codec.JT808Body;
import com.gjxx.netty.codec.TPMSConsts;
import com.gjxx.netty.req.VideoPlayParam;
import com.gjxx.netty.session.Session;
import com.gjxx.netty.session.SessionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.List;

/**
 * 调用终端下行服务
 * <AUTHOR>
 * @date 2018/06/04
 */
@Component
public class TerminalMsgProcessDownServiceImpl extends BaseMsgProcessService implements TerminalMsgProcessDownService {

	private final Logger log = LoggerFactory.getLogger(getClass());
	private MyEncoder msgEncoder = new MyEncoder();
	private SessionManager sessionManager = SessionManager.getInstance();
	private BitOperator bitOperator = new BitOperator();
	private BCD8421Operater bcd8421Operater = new BCD8421Operater();
	@Autowired
	private RedisCache redisCache;
	@Autowired
	private CarDevOnlineStatusService carDevOnlineStatusService;
	@Autowired
	private VideoPlayConfig videoPlayConfig;

	@Override
	public R<String> textSend(TextSendDTO textSendDTO) {
		Session session = sessionManager.findByTerminalPhone(textSendDTO.getSimNo());
		if(session == null){
			redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + textSendDTO.getSimNo());
			carDevOnlineStatusService.delete(textSendDTO.getSimNo());
			return R.fail("设备不在线!");
		}
		byte[] GBKDecode = new byte[0];
		try {
			GBKDecode = textSendDTO.getText().getBytes("GBK");// 文本信息
		} catch (Exception e) {
			e.printStackTrace();
			log.error("文本消息下发转换文本消息异常:{}"+e.getMessage());
			return R.fail("转换文本消息异常!");
		}
		byte[] msgBody = this.bitOperator.concatAll(Arrays.asList(
				bitOperator.integerTo2Bytes(Integer.parseInt(textSendDTO.getMsgType())), // 文本信息标志 2-终端显示器显示
				GBKDecode// 文本信息
		));
		byte[] bs = new byte[0];
		int flowId = session.currentFlowId();
		try {
			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_text_send,flowId);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("文本消息下发转换消息异常:{}"+e.getMessage());
			return R.fail("转换消息异常!");
		}
		try {
			boolean bool = super.sendClient(session.getChannel(), bs);
			if(!bool){
				return R.fail("发送数据出错!");
			}else {
				String keyStr = String.valueOf(TPMSConsts.cmd_text_send)+flowId;
				for(int i = 0;i<10;i++){
					Thread.sleep(1000);
					String value =redisCache.getCacheObject(keyStr);
					if(value != null){
						redisCache.deleteObject(keyStr);
						if(value.equals("0")){
							return R.ok("文本信息下发成功!");
						}else {
							return R.fail("未知错误!");
						}
					}
				}
				return R.fail("设备响应超时!");
			}
		} catch (InterruptedException e) {
			e.printStackTrace();
			log.error("文本消息下发发送消息异常:{}"+e.getMessage());
			return R.fail("发送消息异常!");
		}
	}

	@Override
	public  R<String> photoScreen(PhotoScreenDTO photoScreenDTO) {
		Session session = sessionManager.findByTerminalPhone(photoScreenDTO.getSimNo());
		if(session == null){
			redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + photoScreenDTO.getSimNo());
			carDevOnlineStatusService.delete(photoScreenDTO.getSimNo());
			return R.fail("设备不在线!");
		}
		byte[] msgBody = JT808Body.get8801Body(photoScreenDTO.getChnId(),
				"1","1","0",photoScreenDTO.getResolution(),
				photoScreenDTO.getImgQuality(),photoScreenDTO.getBright(),photoScreenDTO.getContrast(),
				photoScreenDTO.getSaturation(),photoScreenDTO.getChroma());
		byte[] bs = new byte[0];
		int flowId = session.currentFlowId();
		try {
			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_photo_screen,flowId);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("立即拍摄转换消息异常:{}"+e.getMessage());
			return R.fail("转换消息异常!");
		}
		try {
			boolean bool = super.sendClient(session.getChannel(), bs);
			if(!bool){
				return R.fail("发送数据出错!");
			}else {
				String keyStr = String.valueOf(TPMSConsts.cmd_photo_screen)+flowId;
				for(int i = 0;i<10;i++){
					Thread.sleep(1000);
					String value =redisCache.getCacheObject(keyStr);
					if(value != null){
						redisCache.deleteObject(keyStr);
						if(value.equals("0")){
							return R.ok("拍照成功!");
						}else if(value.equals("1")){
							return R.fail("拍照失败!");
						}else if(value.equals("2")){
							return R.fail("通道不支持!");
						}else {
							return R.fail("未知错误!");
						}
					}
				}
				return R.fail("设备响应超时!");
			}
		} catch (InterruptedException e) {
			e.printStackTrace();
			log.error("立即拍摄发送消息异常:{}"+e.getMessage());
			return R.fail("发送消息异常!");
		}
	}

	@Override
	public R<ParamSelectVO> paramSelect(ParamSelectDTO paramSelectDTO) {
		Session session = sessionManager.findByTerminalPhone(paramSelectDTO.getSimNo());
		if(session == null){
			redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + paramSelectDTO.getSimNo());
			carDevOnlineStatusService.delete(paramSelectDTO.getSimNo());
			return R.fail("设备不在线!");
		}
		byte[] msgBody = new byte[0];
		byte[] bs = new byte[0];
		int flowId = session.currentFlowId();
		try {
			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_terminal_param_query,flowId);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("参数查询转换消息异常:{}"+e.getMessage());
			return R.fail("转换消息异常!");
		}
		try {
			boolean bool = super.sendClient(session.getChannel(), bs);
			if(!bool){
				return R.fail("发送数据出错!");
			}else {
				String keyStr = String.valueOf(TPMSConsts.cmd_terminal_param_query)+flowId;
				for(int i = 0;i<10;i++){
					Thread.sleep(1000);
					String value =redisCache.getCacheObject(keyStr);
					if(value != null){
						redisCache.deleteObject(keyStr);
						ParamQueryMsg paramQueryMsg = JSON.parseObject(value, ParamQueryMsg.class);
						if(paramQueryMsg.getParamNum() == 0){
							return R.fail("参数查询失败!");
						}
						List<ParamQueryMsg.Param> paramList = paramQueryMsg.getParamList();
						ParamSelectVO paramSelectVO = new ParamSelectVO();
						for(ParamQueryMsg.Param param:paramList){
							if(0x0020 == param.getParamId()){
								paramSelectVO.setLocationStrategy(param.getParamValue());
							}
							if(0x0021 == param.getParamId()){
								paramSelectVO.setLocationProject(param.getParamValue());
							}
							if(0x0001 == param.getParamId()){
								paramSelectVO.setHeartInterval(param.getParamValue());
							}
							if(0x0027 == param.getParamId()){
								paramSelectVO.setDormancyInterval(param.getParamValue());
							}
							if(0x0028 == param.getParamId()){
								paramSelectVO.setUrgentWarningInterval(param.getParamValue());
							}
							if(0x0029 == param.getParamId()){
								paramSelectVO.setDefaultInterval(param.getParamValue());
							}
						}
						return R.ok(paramSelectVO);
					}
				}
				return R.fail("设备响应超时!");
			}
		} catch (InterruptedException e) {
			e.printStackTrace();
			log.error("参数查询发送消息异常:{}"+e.getMessage());
			return R.fail("发送消息异常!");
		}
	}

	@Override
	public R<String> paramSet(ParamSetDTO paramSetDTO) {
		Session session = sessionManager.findByTerminalPhone(paramSetDTO.getSimNo());
		if(session == null){
			redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + paramSetDTO.getSimNo());
			carDevOnlineStatusService.delete(paramSetDTO.getSimNo());
			return R.fail("设备不在线!");
		}
		byte[] msgBody = JT808Body.get8103Body(paramSetDTO.getLocationStrategy(),paramSetDTO.getLocationProject(),
				paramSetDTO.getHeartInterval(),paramSetDTO.getDormancyInterval(),paramSetDTO.getUrgentWarningInterval(),paramSetDTO.getDefaultInterval());
		byte[] bs = new byte[0];
		int flowId = session.currentFlowId();
		try {
			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_terminal_param_settings,flowId);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("参数设置转换消息异常:{}"+e.getMessage());
			return R.fail("转换消息异常!");
		}
		try {
			boolean bool = super.sendClient(session.getChannel(), bs);
			if(!bool){
				return R.fail("发送数据出错!");
			}else {
				String keyStr = String.valueOf(TPMSConsts.cmd_terminal_param_settings)+flowId;
				for(int i = 0;i<10;i++){
					Thread.sleep(1000);
					String value =redisCache.getCacheObject(keyStr);
					if(value != null){
						redisCache.deleteObject(keyStr);
						if(value.equals("0")){
							return R.ok("参数设置成功!");
						}else {
							return R.fail("未知错误!");
						}
					}
				}
				return R.fail("设备响应超时!");
			}
		} catch (InterruptedException e) {
			e.printStackTrace();
			log.error("参数设置发送消息异常:{}"+e.getMessage());
			return R.fail("发送消息异常!");
		}
	}

	@Override
	public R<String> phoneCar(PhoneCarDTO phoneCarDTO) {
		Session session = sessionManager.findByTerminalPhone(phoneCarDTO.getSimNo());
		if(session == null){
			redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + phoneCarDTO.getSimNo());
			carDevOnlineStatusService.delete(phoneCarDTO.getSimNo());
			return R.fail("设备不在线!");
		}
		byte[] msgBody = JT808Body.get8400Body(phoneCarDTO.getPhoneCarType(),phoneCarDTO.getPhoneNo());
		byte[] bs = new byte[0];
		int flowId = session.currentFlowId();
		try {
			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_phone_call,flowId);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("电话回拨转换消息异常:{}"+e.getMessage());
			return R.fail("转换消息异常!");
		}
		try {
			boolean bool = super.sendClient(session.getChannel(), bs);
			if(!bool){
				return R.fail("发送数据出错!");
			}else {
				String keyStr = String.valueOf(TPMSConsts.cmd_phone_call)+flowId;
				for(int i = 0;i<10;i++){
					Thread.sleep(1000);
					String value =redisCache.getCacheObject(keyStr);
					if(value != null){
						redisCache.deleteObject(keyStr);
						if(value.equals("0")){
							return R.ok("电话回拨发送成功!");
						}else {
							return R.fail("未知错误!");
						}
					}
				}
				return R.fail("设备响应超时!");
			}
		} catch (InterruptedException e) {
			e.printStackTrace();
			log.error("电话回拨发送消息异常:{}"+e.getMessage());
			return R.fail("发送消息异常!");
		}
	}

	@Override
	public R<String> locationQuery(String phone) {
		Session session = sessionManager.findByTerminalPhone(phone);
		if(session == null){
			redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + phone);
			carDevOnlineStatusService.delete(phone);
			return R.fail("设备不在线!");
		}
		byte[] msgBody = new byte[0];
		byte[] bs = new byte[0];
		int flowId = session.currentFlowId();
		try {
			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_location_query,flowId);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("位置信息查询转换消息异常:{}"+e.getMessage());
			return R.fail("转换消息异常!");
		}
		try {
			boolean bool = super.sendClient(session.getChannel(), bs);
			if(!bool){
				return R.fail("发送数据出错!");
			}else {
				return R.ok(String.valueOf(flowId));
			}
		} catch (InterruptedException e) {
			e.printStackTrace();
			log.error("位置信息查询发送消息异常:{}"+e.getMessage());
			return R.fail("发送消息异常!");
		}
	}

	@Override
	public R<String> realPlayRequest(VideoPlayParam videoPlayParam) {
		log.info("视频播放请求:{}", JSON.toJSONString(videoPlayParam,JSONWriter.Feature.PrettyFormat));
		Session session = sessionManager.findByTerminalPhone(videoPlayParam.getMobileNumber());
		if(session == null){
			redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + videoPlayParam.getMobileNumber());
			carDevOnlineStatusService.delete(videoPlayParam.getMobileNumber());
			return R.fail("设备不在线!");
		}
		byte[] msgBody = JT808Body.get9101Body(videoPlayParam);
		byte[] bs = new byte[0];
		int flowId = session.currentFlowId();
		try {
			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_video_request,flowId);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("视频播放转换消息异常:{}"+e.getMessage());
			return R.fail("转换消息异常!");
		}
		try {
			boolean bool = super.sendClient(session.getChannel(), bs);
			if(!bool){
				return R.fail("发送数据出错!");
			}else {
				return R.ok("OK");
			}
		} catch (InterruptedException e) {
			e.printStackTrace();
			log.error("视频播放发送消息异常:{}"+e.getMessage());
			return R.fail("发送消息异常!");
		}
	}

	@Override
	public R<String> realPlayControl(VideoPlayParam videoPlayParam) {
		log.info("实时音视频传输控制请求:{}", JSON.toJSONString(videoPlayParam,JSONWriter.Feature.PrettyFormat));
		Session session = sessionManager.findByTerminalPhone(videoPlayParam.getMobileNumber());
		if(session == null){
			redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + videoPlayParam.getMobileNumber());
			carDevOnlineStatusService.delete(videoPlayParam.getMobileNumber());
			return R.fail("设备不在线!");
		}
		byte[] msgBody = JT808Body.get9102Body(videoPlayParam);
		byte[] bs = new byte[0];
		int flowId = session.currentFlowId();
		try {
			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_video_control,flowId);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("实时音视频传输控制转换消息异常:{}"+e.getMessage());
			return R.fail("转换消息异常!");
		}
		try {
			boolean bool = super.sendClient(session.getChannel(), bs);
			if(!bool){
				return R.fail("实时音视频传输控制发送数据出错!");
			}else {
				return R.ok("OK");
			}
		} catch (InterruptedException e) {
			e.printStackTrace();
			log.error("实时音视频传输控制发送消息异常:{}"+e.getMessage());
			return R.fail("发送消息异常!");
		}
	}

	@Override
	public R<HistoryVideoMsg>  historyVideo(String selectCarDevChn, String mobileNumber, String startTime, String endTime) {
		log.info("获取终端历史视频资源列表请求:车辆设备ID：{}，手机号：{}，开始时间：{}，结束时间：{}", selectCarDevChn, mobileNumber, startTime, endTime);
		int i_chn = Integer.parseInt(selectCarDevChn);
		Session session = sessionManager.findByTerminalPhone(mobileNumber);
		if(session == null){
			return R.fail("设备不在线");
		}
		byte[] msgBody = JT808Body.get9205Body(i_chn,startTime,endTime);//逻辑通道号,0 表示所有通道
		byte[] bs = new byte[0];
		int flowId = session.currentFlowId();
		try {
			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_history_video,flowId);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("获取终端历史视频资源列表转换消息异常:{}"+e.getMessage());
			return R.fail("转换消息异常!");
		}
		try {
			boolean bool = super.sendClient(session.getChannel(), bs);
			if(!bool){
				return R.fail("获取终端历史视频资源列表发送数据出错!");
			}
			for(int i = 0;i<20;i++){
				Thread.sleep(500);
				String value = redisCache.getCacheObject(TPMSConsts.cmd_history_video+String.valueOf(flowId));
				if(value != null && !"".equals(value) && !"0".equals(value)){
					HistoryVideoMsg historyVideoMsg = JSON.parseObject(value, HistoryVideoMsg.class);
					return R.ok(historyVideoMsg);
				}
			}
			return R.fail("未获取到录像资源列表!");
		} catch (InterruptedException e) {
			e.printStackTrace();
			log.error("获取终端历史视频资源列表发送消息异常:{}"+e.getMessage());
			return R.fail("发送消息异常!");
		}
	}

	@Override
	public R<VideoRtmpParamReturn> playbackRequest(VideoParamBean videoParam) {
		Session session = sessionManager.findByTerminalPhone(videoParam.getMobileNumber());
		if(session == null){
			return R.fail("设备不在线");
		}
		byte[] msgBody = JT808Body.get9201Body(videoParam);
		byte[] bs = new byte[0];
		int flowId = session.currentFlowId();
		try {
			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_video_playback_request,flowId);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("历史视频播放转换消息异常:{}"+e.getMessage());
			return R.fail("转换消息异常!");
		}
		try {
			boolean bool = super.sendClient(session.getChannel(), bs);
			if(!bool){
				return R.fail("历史视频播放发送数据出错!");
			}
//			String keyStr = String.valueOf(TPMSConsts.cmd_video_playback_request)+String.valueOf(flowId);
			if(videoParam.getMobileNumber().length() < 12){
				videoParam.setMobileNumber("0"+videoParam.getMobileNumber());
			}
			VideoRtmpParamReturn videoRtmpParam = new VideoRtmpParamReturn();
			videoRtmpParam.setVehicleId(videoParam.getVehicleId());
			videoRtmpParam.setLogicalChn(String.valueOf(videoParam.getMsg().getLogicalChn()));
			String playUrl = "http://"+videoParam.getMsg().getSinkIP()+":"
					+videoPlayConfig.getSinkPlayPort()+"/video/"
					+videoParam.getMobileNumber()+"-"+videoParam.getMsg().getLogicalChn();
			videoRtmpParam.setPlayUrl(playUrl);
			return R.ok(videoRtmpParam);
		} catch (InterruptedException e) {
			e.printStackTrace();
			log.error("历史视频播放发送消息异常:{}"+e.getMessage());
			return R.fail("发送消息异常!");
		}
	}


	@Override
	public R<String> playbackControl(VideoPlayParam videoPlayParam) {
		log.info("向终端下发远程录像回放控制请求:{}", JSON.toJSONString(videoPlayParam,JSONWriter.Feature.PrettyFormat));
		Session session = sessionManager.findByTerminalPhone(videoPlayParam.getMobileNumber());
		if(session == null){
			redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + videoPlayParam.getMobileNumber());
			carDevOnlineStatusService.delete(videoPlayParam.getMobileNumber());
			return R.fail("设备不在线!");
		}
		byte[] msgBody = JT808Body.get9202Body(videoPlayParam);
		byte[] bs = new byte[0];
		int flowId = session.currentFlowId();
		try {
			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_video_playback_control,flowId);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("向终端下发远程录像回放控制转换消息异常:{}"+e.getMessage());
			return R.fail("转换消息异常!");
		}
		try {
			boolean bool = super.sendClient(session.getChannel(), bs);
			if(!bool){
				return R.fail("向终端下发远程录像回放控制发送数据出错!");
			}else {
				return R.ok("OK");
			}
		} catch (InterruptedException e) {
			e.printStackTrace();
			log.error("向终端下发远程录像回放控制发送消息异常:{}"+e.getMessage());
			return R.fail("发送消息异常!");
		}
	}
//
//	@Override
//	public VideoReturn realPlayNotifyStatus(VideoParam videoParam) {
//		VideoReturn videoReturn = new VideoReturn();
//		videoReturn.setResultCode(1);//1-失败
//		videoReturn.setResultMsg("设备响应超时!");
//		if(videoParam.getMobileNumber() != null && videoParam.getMobileNumber().length()>11){
//			if("0".equals(videoParam.getMobileNumber().substring(0,1))){
//				videoParam.setMobileNumber(videoParam.getMobileNumber().substring(1));
//			}
//		}
//		Session session = sessionManager.findByTerminalPhone(videoParam.getMobileNumber());
//		if(session == null){
//			videoReturn.setResultMsg("终端不在线!");
//			return videoReturn;
//		}
//		byte[] msgBody = JT808Body.get9105Body(videoParam);
//		byte[] bs = new byte[0];
//		int flowId = session.currentFlowId();
//		try {
//			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_video_status,flowId);
//		} catch (Exception e) {
//			e.printStackTrace();
//			log.error("转换消息异常:{}"+e.toString());
//			videoReturn.setResultMsg("转换消息异常!");
//			return videoReturn;
//		}
//		try {
//			boolean bool = super.sendClient(session.getChannel(), bs);
//			if(!bool){
//				videoReturn.setResultMsg("发送数据出错!");
//				return videoReturn;
//			}
//			String keyStr = String.valueOf(TPMSConsts.cmd_video_status)+String.valueOf(flowId);
//			Jedis jedis = RedisUtil.getJedis();
//			for(int i = 0;i<10;i++){
//				Thread.sleep(1000);
//				String value =jedis.get(keyStr);
//				if(value != null){
//					if(value.equals("0")){
//						videoReturn.setResultCode(0);//0-成功
//						videoReturn.setResultMsg("成功!");
//					}else if(value.equals("1")){
//						videoReturn.setResultMsg("失败!");
//					}else if(value.equals("2")){
//						videoReturn.setResultMsg("消息有误!");
//					}else if(value.equals("3")){
//						videoReturn.setResultMsg("不支持!");
//					}
//					jedis.del(keyStr);
//					break;
//				}
//			}
//			jedis.close();
//			return videoReturn;
//		} catch (InterruptedException e) {
//			e.printStackTrace();
//			log.error("发送消息异常:{}"+e.toString());
//			videoReturn.setResultMsg("发送消息异常!");
//			return videoReturn;
//		}
//	}
//


//
//	@Override
//	public VideoReturn getAVideoAttributes(VideoParam videoParam) {
//		VideoReturn videoReturn = new VideoReturn();
//		videoReturn.setResultCode(1);//1-失败
//		videoReturn.setResultMsg("设备响应超时!");
//		if(videoParam.getMobileNumber() != null && videoParam.getMobileNumber().length()>11){
//			if("0".equals(videoParam.getMobileNumber().substring(0,1))){
//				videoParam.setMobileNumber(videoParam.getMobileNumber().substring(1));
//			}
//		}
////		if("粤B99999".equals(videoParam.getPlateNo())){
////			return videoReturn;
////		}
////		if("粤B00001".equals(videoParam.getPlateNo())){
////			return videoReturn;
////		}
//		Session session = sessionManager.findByTerminalPhone(videoParam.getMobileNumber());
//		if(session == null){
//			videoReturn.setResultMsg("终端不在线!");
//			return videoReturn;
//		}
//		byte[] msgBody = new byte[0];
//		byte[] bs = new byte[0];
//		int flowId = session.currentFlowId();
//		try {
//			bs = this.msgEncoder.encode4ParamSetting(msgBody,session, TPMSConsts.cmd_terminal_video_attributes,flowId);
//		} catch (Exception e) {
//			e.printStackTrace();
//			log.error("转换消息异常:{}"+e.toString());
//			videoReturn.setResultMsg("转换消息异常!");
//			return videoReturn;
//		}
//		try {
//			boolean bool = super.sendClient(session.getChannel(), bs);
//			if(!bool){
//				videoReturn.setResultMsg("发送数据出错!");
//				return videoReturn;
//			}
//			String keyStr = String.valueOf(TPMSConsts.cmd_terminal_video_attributes)+videoParam.getMobileNumber();
//			Jedis jedis = RedisUtil.getJedis();
//			for(int i = 0;i<10;i++){
//				Thread.sleep(1000);
//				String value =jedis.get(keyStr);
//				if(value != null && !"".equals(value) && !"0".equals(value)){
//					videoReturn.setResultCode(0);
//					VideoAttrMsg videoAttrMsg = JsonUtils.parseJsonToObj(value, VideoAttrMsg.class);
//					videoReturn.setaVideoAttributes(videoAttrMsg);
//					videoReturn.setResultMsg("成功!");
//					jedis.del(keyStr);
//					break;
//				}
//			}
//			jedis.close();
//			return videoReturn;
//		} catch (InterruptedException e) {
//			e.printStackTrace();
//			log.error("发送消息异常:{}"+e.toString());
//			videoReturn.setResultMsg("发送消息异常!");
//			return videoReturn;
//		}
//	}
//
}
