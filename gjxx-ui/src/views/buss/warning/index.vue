<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="车辆标识" prop="vehicleId">
        <el-input
          v-model="queryParams.vehicleId"
          placeholder="请输入车辆标识"
          clearable
          @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="SIM卡号" prop="simNo">
        <el-input
          v-model="queryParams.simNo"
          placeholder="请输入SIM卡号"
          clearable
          @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="报警类型" prop="warningType">
        <el-select
          v-model="queryParams.warningType"
          placeholder="报警类型"
          clearable
          filterable
          style="width: 240px">
          <el-option
            v-for="dict in dict.type.t_warning_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" prop="handleStatus">
        <el-select
          v-model="queryParams.handleStatus"
          placeholder="处理状态"
          clearable
          style="width: 240px">
          <el-option
            v-for="dict in dict.type.t_handle_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="第一次报警时间" prop="createTime">
        <el-date-picker
          v-model="daterangeDate"
          value-format="yyyyMMddHHmmss"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :picker-options="pickerOptions"
          clearable
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleBatch"
          v-hasPermi="['buss:warning:handleBatch']">批量处理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleNo"
          v-hasPermi="['buss:warning:handleNo']">暂不处理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['buss:warning:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="warningList" @selection-change="handleSelectionChange" @sort-change="handleSortChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="ID" align="center" prop="id" />-->
      <el-table-column label="车辆标识" align="center" prop="vehicleId" />
      <el-table-column label="SIM卡号" align="center" prop="simNo" />
      <el-table-column label="报警类型" align="center" prop="warningType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_warning_type" :value="scope.row.warningType"/>
        </template>
      </el-table-column>
      <el-table-column label="报警次数" align="center" prop="warningCount" :sort-orders="['descending','ascending']" sortable="custom"/>
      <el-table-column label="处理状态" align="center" prop="handleStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_handle_status" :value="scope.row.handleStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="第一次报警时间" align="center" prop="createTime" width="180" :sort-orders="['descending','ascending']" sortable="custom">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后一次报警时间" align="center" prop="updateTime" width="180" :sort-orders="['descending','ascending']" sortable="custom">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="center" prop="handleUser" />
      <el-table-column label="处理时间" align="center" prop="handleTime" width="180" :sort-orders="['descending','ascending']" sortable="custom">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.handleTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180"  align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" >
          <el-button v-if="scope.row.handleStatus === '01' || scope.row.handleStatus === '03'"
            size="mini"
            type="text"
            @click="handleBatch(scope.row)"
            v-hasPermi="['buss:warning:handleBatch']">处理</el-button>
          <el-button v-if="scope.row.handleStatus === '01'"
            size="mini"
            type="text"
            @click="handleNo(scope.row)"
            v-hasPermi="['buss:warning:handleNo']">暂不处理</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row,scope.index)"
            v-hasPermi="['buss:warning:query']">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"/>

    <!-- 报警信息详情对话框 -->
    <el-dialog title="报警信息详情" :visible.sync="details" width="60%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="180px">
        <el-row>
            <el-col :span="12">
              <el-form-item label="车辆标识：">{{ form.vehicleId }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="SIM卡号：">{{ form.simNo }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报警类型：">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.t_warning_type" :value="form.warningType"/>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报警次数：">{{ form.warningCount }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处理状态：">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.t_handle_status" :value="form.handleStatus"/>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处理人：">{{ form.handleUser }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处理时间：">{{ parseTime(form.handleTime) }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="第一次报警时间：">{{ parseTime(form.createTime) }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最后一次报警时间：">{{ parseTime(form.updateTime) }}</el-form-item>
            </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="details = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listWarning,  handleBatch, handleNo
} from "@/api/buss/warning";
import moment from "moment";

export default {
  name: "Warning",
  dicts: ['t_handle_status','t_warning_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 报警信息表格数据
      warningList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层
      details: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        simNo: null,
        handleStatus: null,
        startTime: null,
        endTime: null
      },
      daterangeDate: [],//时间范围
      pickerOptions: {
        shortcuts: [{
          text: '最近一小时',
          onClick(picker) {
            const end = moment(new Date()).format("YYYYMMDDHHmmss");
            const start = moment(new Date()).subtract(1, "hours").format("YYYYMMDDHHmmss");
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三小时',
          onClick(picker) {
            const end = moment(new Date()).format("YYYYMMDDHHmmss");
            const start = moment(new Date()).subtract(3, "hours").format("YYYYMMDDHHmmss");
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '今天',
          onClick(picker) {
            const date = moment().format("YYYYMMDD");
            const end = date + '235959';
            const start = date + '000000';
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const date = moment().subtract(1, "days").format("YYYYMMDD").trim();
            const end = date + '235959';
            const start = date + '000000';
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询报警信息列表 */
    getList() {
      this.loading = true;
      listWarning(this.addDateRange(this.queryParams, this.daterangeDate)).then(response => {
        this.warningList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        simNo: null,
        warningType: null,
        warningCount: null,
        handleStatus: null,
        handleUser: null,
        handleTime: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.daterangeDate = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    // 动态取值排序
    handleSortChange(column) {
      this.queryParams.orderByColumn = column.prop;//查询字段是表格中字段名字
      this.queryParams.isAsc = column.order;//动态取值排序顺序
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加报警信息";
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.details = true;
      this.form = row;
    },
    /** 批量处理操作 */
    handleBatch(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认处理报警信息？').then(function() {
        return handleBatch(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("处理成功");
      }).catch(() => {});
    },
    /** 暂不处理操作 */
    handleNo(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认暂不处理报警信息？').then(function() {
        return handleNo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("暂不处理成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('buss/warning/export', {
        ...this.queryParams
      }, `warning_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
