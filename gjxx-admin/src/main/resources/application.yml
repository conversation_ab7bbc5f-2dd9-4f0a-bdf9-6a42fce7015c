# 项目相关配置
gjxx:
  # 名称
  name: gjxx-gps
  # 版本
  version: 1.1.1
  # 版权年份
  copyrightYear: 2024
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/gjxx/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /gjxx-gps/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  log-name: ${spring.application.name}
  log-path: /log/${spring.application.name}
  log-pattern: '%d{yyyy-MM-dd}'
  log-max-history: 60
  log-total-size: 15GB
  level:
    com.gjxx: info
    org.springframework: info

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 50
    # 密码锁定时间（默认10分钟）
    lockTime: 10

publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJtBEwWHn5JW0UjzYtVFeYwTX7aRI7cSmOkXQNMa261rZbRbhZEpn/3CPwhHSFzPaPczU+V+6LpfjHSBhnIS7TUCAwEAAQ==
# Spring配置
spring:
  application:
    name: @project.artifactId@
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: @profileActive@
  #文件上传
  servlet:
    multipart:
      #单个文件大小
      max-file-size: 10MB
      #设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
  # rabbitmq 配置
  rabbitmq:
    host: 127.0.0.1
    port: 5672
    username: admin
    password: 123456
  # mongodb 配置
  data:
    mongodb:
      host: 127.0.0.1
      port: 27017
      # 认证数据库
      database: gjxx-gps
      authenticationDatabase: gjxx-gps
      username: gjxx_admin
      password: Gjxx@123456
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 14
    # 密码
    password: Gjxx#123456
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: **********************************************************************************************************************************************************************************************
        username: root
        password: 123456
        #connection-properties: config.decrypt=true;config.decrypt.key=${publicKey}
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: gjxx
        login-password: Gps@1q2w3e4r
      filter:
        config:
          enabled: true
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

#mybatis
mybatis-plus:
  type-aliases-package: com.gjxx.**.domain
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  config-location: classpath:mybatis/mybatis-config.xml
  global-config:
    db-config:
      #全局默认主键类型 com.baomidou.mybatisplus.annotation.IdType 默认值：ASSIGN_ID
      id-type: ASSIGN_ID
    configuration:
      #配置返回数据库(column下划线命名&&返回java实体是驼峰命名)，自动匹配无需as（没开启这个，SQL需要写as： select user_id as userId）
      map-underscore-to-camel-case: true
      cache-enabled: true

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: false
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# Minio配置
minio:
  url: http://tonnyinfo.xicp.net:9000
  accessKey: minio
  secretKey: <EMAIL>
  bucketName: gjxx-gps
  baseUrl: http://tonnyinfo.xicp.net:9000

# 视频播放相关配置
video:
  # 视频服务器地址
  sinkIP: 127.0.0.1
  # 视频服务器端口
  sinkPort: 11935
  # 视频播放端口
  sinkPlayPort: 17789
  # 视频播放IP地址
  sinkPlayIP: 127.0.0.1
  # 视频播放第二个端口
  sinkPlayTwoPort: 9501
  # 视频播放第三个端口
  sinkPlayThreePort: 9502
  # 视频播放第四个端口
  sinkPlayFourPort: 9505
  # 视频播放第五个端口
  sinkPlayFivePort: 9506
