<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="车队简称" prop="nameAlias">
        <el-input
          v-model="queryParams.nameAlias"
          placeholder="请输入车队简称"
          clearable
          @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="车队名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入车队名称"
          clearable
          @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="是否为公司" prop="whetherCompany">
        <el-select
          v-model="queryParams.whetherCompany"
          placeholder="是否为公司"
          clearable
          style="width: 240px">
          <el-option
            v-for="dict in dict.type.sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['buss:carTeam:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
        >展开/折叠</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-if="refreshTable"
              v-loading="loading"
              :data="carTeamList"
              row-key="id"
              :default-expand-all="isExpandAll"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
      <el-table-column label="车队简称" align="center" prop="nameAlias" :show-overflow-tooltip="true"/>
      <el-table-column label="车队名称" align="center" prop="name" />
      <el-table-column label="排序" align="center" prop="orderNum" />
      <el-table-column label="是否为公司" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.whetherCompany"/>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createUser" />
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改人" align="center" prop="updateUser" />
      <el-table-column label="修改时间" align="center" prop="updateTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180"  align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['buss:carTeam:edit']">修改</el-button>
          <el-button v-if="scope.row.whetherCompany == 'N'"
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(scope.row)"
            v-hasPermi="['buss:carTeam:add']">新增</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['buss:carTeam:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改车队对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="680px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级车队" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="carTeamOptions"
                :normalizer="normalizer"
                :show-count="true"
                placeholder="选择上级车队"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否为公司">
              <el-select v-model="form.whetherCompany" :disabled="isEditPage" placeholder="是否为公司">
                <el-option
                  v-for="dict in dict.type.sys_yes_no"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车队名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入车队名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车队简称" prop="nameAlias">
              <el-input v-model="form.nameAlias" placeholder="请输入车队简称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listCarTeam, getCarTeam, delCarTeam,
  addCarTeam, updateCarTeam } from "@/api/buss/carTeam";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "CarTeam",
  dicts: ['sys_yes_no'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 车队表格树数据
      carTeamList: [],
      // 车队树选项
      carTeamOptions: [],
      // 是否修改页面
      isEditPage: true,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        name: undefined,
        nameAlias: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        whetherCompany: [
          { required: true, message: "是否为公司不能为空", trigger: "blur" }
        ],
        name: [
          { required: true, message: "车队名称不能为空", trigger: "blur" }
        ],
        nameAlias: [
          { required: true, message: "车队简称不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询车队信息列表 */
    getList() {
      this.loading = true;
      listCarTeam(this.queryParams).then(response => {
        this.carTeamList = this.handleTree(response.data, "id");
        this.loading = false;
      });
    },
    /** 转换车队数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.nameAlias,
        children: node.children
      };
    },
    /** 查询车队下拉树结构 */
    getTreeselect() {
      listCarTeam().then(response => {
        this.carTeamOptions = [];
        const carTeam = { id: 0, nameAlias: '主类目', children: [] };
        carTeam.children = this.handleTree(response.data, "id");
        this.carTeamOptions.push(carTeam);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        nameAlias: null,
        parentId: null,
        orderNum: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.isEditPage = false;
      this.reset();
      this.getTreeselect();
      if (row != null && row.id) {
        this.form.parentId = row.id;
      } else {
        this.form.parentId = 0;
      }
      this.open = true;
      this.title = "添加车队";
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.isEditPage = true;
      this.reset();
      this.getTreeselect();
      getCarTeam(row.id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改车队信息";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            updateCarTeam(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCarTeam(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除车队简称为"' + row.nameAlias + '"的数据项？').then(function() {
        return delCarTeam(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
