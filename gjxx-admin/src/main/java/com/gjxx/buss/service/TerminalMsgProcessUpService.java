package com.gjxx.buss.service;

import com.gjxx.common.tcp.*;
import com.gjxx.netty.req.*;

/**
 * 终端调用上行服务
 * <AUTHOR>
 * @date 2018/06/04
 */
public interface TerminalMsgProcessUpService {
    /**
     * 终端注册
     * <AUTHOR>
     * @date 2018/6/6 0006 11:34
     * @return void
     */
    void processRegisterMsg(TerminalRegisterMsg msg) throws Exception;
    /**
     * 终端鉴权
     * <AUTHOR>
     * @date 2018/6/6 0006 11:34
     * @return void
     */
    void processAuthMsg(TerminalAuthenticationMsg msg) throws Exception;
    /**
     * 终端心跳
     * <AUTHOR>
     * @date 2018/6/6 0006 11:34
     * @return void
     */
    void processTerminalHeartBeatMsg(PackageData req) throws Exception;
    /**
     * 终端注销
     * <AUTHOR>
     * @date 2018/6/6 0006 11:34
     * @return void
     */
    void processTerminalLogoutMsg(PackageData req) throws Exception;
    /**
     * 位置信息上报
     * <AUTHOR>
     * @date 2018/6/6 0006 11:34
     * @return void
     */
    void processLocationInfoUploadMsg(LocationInfoUploadMsg req) throws Exception;
    /**
     * 位置信息查询应答
     * <AUTHOR>
     * @date 2018/6/6 0006 11:34
     * @return void
     */
    void locationInfoQueryMsg(LocationInfoQueryMsg req) throws Exception;
    /**
     * 查询终端参数应答
     * <AUTHOR>
     * @date 2018/6/6 0006 11:34
     * @return void
     */
    void paramQueryMsg(PackageData req) throws Exception;
    /**
     * 摄像头立即拍摄命令应答
     * <AUTHOR>
     * @date 2018/6/6 0006 11:34
     * @return void
     */
    void photoScreenMsg(PhotoScreenMsg req) throws Exception;
    /**
     * 多媒体数据上传
     * <AUTHOR>
     * @date 2018/6/6 0006 11:34
     * @return void
     */
    void mediaFileMsg(PackageData req) throws Exception;
    /**
     * 终端通用应答
     * <AUTHOR>
     * @date 2018/6/6 0006 11:34
     * @return void
     */
    void processTerminalCommonMsg(TerminalCommonMsg req) throws Exception;
    /**
     * 终端上传音视频资源列表
     * <AUTHOR>
     * @date 2018/6/6 0006 11:34
     * @return void
     */
    void historyVideoMsg(PackageData packageData) throws Exception;
    /**
     * 终端上传音视频属性
     * <AUTHOR>
     * @date 2018/6/6 0006 11:34
     * @return void
     */
    void videoAttrUploadMsg(PackageData packageData) throws Exception;
}
