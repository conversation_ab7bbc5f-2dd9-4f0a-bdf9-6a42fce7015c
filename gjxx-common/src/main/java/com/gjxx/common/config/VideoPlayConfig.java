package com.gjxx.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 视频播放相关配置
 */
@Component
@ConfigurationProperties(prefix = "video")
@Data
public class VideoPlayConfig {
    /**
     * 视频服务器地址
     */
    private String sinkIP;
    /**
     * 视频服务器端口
     */
    private String sinkPort;
    /**
     * 视频播放端口
     */
    private String sinkPlayPort;
    /**
     * 视频播放IP地址
     */
    private String sinkPlayIP;
    /**
     * 视频播放第二个端口
     */
    private String sinkPlayTwoPort;
    /**
     * 视频播放第三个端口
     */
    private String sinkPlayThreePort;
    /**
     * 视频播放第四个端口
     */
    private String sinkPlayFourPort;
    /**
     * 视频播放第五个端口
     */
    private String sinkPlayFivePort;
}
