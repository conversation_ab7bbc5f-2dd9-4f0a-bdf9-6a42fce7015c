package com.gjxx.mongo.dao;


import com.gjxx.mongo.entity.CarDevOnlineStatus;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

/*接口继承此接口MongoRepository<CarDevOnlineStatus,Long>，第二个参数是id属性的包装类  */
public interface CarDevOnlineStatusRepository extends MongoRepository<CarDevOnlineStatus,String> {

    CarDevOnlineStatus findCarDevOnlineStatusById(String id); //此方法不用实现，mongodb帮我们根据我们的名字去实现方法
    //findStuById等方法要按照一定的约束来才可以帮我们实现，查询以findxxxBy等
}


