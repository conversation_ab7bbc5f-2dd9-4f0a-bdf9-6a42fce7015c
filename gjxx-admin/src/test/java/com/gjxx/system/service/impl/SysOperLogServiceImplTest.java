package com.gjxx.system.service.impl;

import com.gjxx.BaseServiceTest;
import com.gjxx.system.domain.SysOperLog;
import com.gjxx.system.service.ISysOperLogService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

//import static org.junit.jupiter.api.Assertions.*;

class SysOperLogServiceImplTest extends BaseServiceTest {

    @Autowired
    private ISysOperLogService operLogService;

    @Test
    void getById() {
        SysOperLog byId = this.operLogService.getById(100L);
        System.out.println(byId);
    }

    @Test
    void selectOperLogById() {
        SysOperLog sysOperLog = this.operLogService.selectOperLogById(100L);
        System.out.println(sysOperLog);
    }
}
