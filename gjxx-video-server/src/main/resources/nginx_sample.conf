# 使用多个域名的模式
# 通过将多个域名都反向代理到音视频服务器的3333端口
# 为单个域名的6路并发，扩展到 n * 6路，每增加一个域名就可以多出6路并发
# 流媒体URL如：
#     http://v1.hentai.org.cn/video/013800138999-2
#     http://v2.hentai.org.cn/video/013800138999-2

server{
	listen 80;
	server_name v1.hentai.org.cn v2.hentai.org.cn v3.hentai.org.cn v4.hentai.org.cn v5.hentai.org.cn v6.hentai.org.cn;
	location /
	{
		proxy_pass http://localhost:3333;
		chunked_transfer_encoding on;
		add_header 'Access-Control-Allow-Origin' '*';
		add_header 'Access-Control-Allow-Credentials' 'true';
	}
	access_log off;
}

# -------------------------------------------------- 华丽的分割线 --------------------------------------------------

# 使用多个端口的模式
# 每多一个端口配置，就可以多出6路并发
# 流媒体URL如：
#     http://***********:1000/video/013800138999-2
#     http://***********:1001/video/013800138999-2
#     http://***********:1002/video/013800138999-2

server{
	listen 9501;
	location /
	{
		proxy_pass http://************:17789;
		chunked_transfer_encoding on;
	}
	access_log off;
}

server{
	listen 9502;
	location /
	{
		proxy_pass http://************:17789;
		chunked_transfer_encoding on;
	}
	access_log off;
}

server{
	listen 1002;
	location /
	{
		proxy_pass http://localhost:3333;
		chunked_transfer_encoding on;
	}
	access_log off;
}
