package com.gjxx;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.gjxx.netty.req.TerminalRegisterMsg;
import org.junit.jupiter.api.Test;

/**
 */
//@ExtendWith(SpringExtension.class)
//@SpringBootTest
public class BaseServiceTest {

//    String url = "http://127.0.0.19:20056/test-api";
//    String url = "http://************:36000/prod-api";
    String url = "http://localhost/dev-api";

    String fileBase64 = "";
    @Test
    public void photoUpload() throws Exception {
        TerminalRegisterMsg msg = new TerminalRegisterMsg();
        TerminalRegisterMsg.TerminalRegInfo terminalRegInfo = new TerminalRegisterMsg.TerminalRegInfo();
        terminalRegInfo.setTerminalId("12313");
        terminalRegInfo.setTerminalType("12313");
        msg.setTerminalRegInfo(terminalRegInfo);
        System.out.println(JSON.toJSONString(msg, JSONWriter.Feature.PrettyFormat));
    }
}
