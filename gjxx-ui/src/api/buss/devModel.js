import request from '@/utils/request'

// 查询设备型号信息列表
export function listDevModel(query) {
  return request({
    url: '/buss/devModel/list',
    method: 'get',
    params: query
  })
}

// 查询设备型号信息详细
export function getDevModel(id) {
  return request({
    url: '/buss/devModel/' + id,
    method: 'get'
  })
}

// 新增设备型号信息
export function addDevModel(data) {
  return request({
    url: '/buss/devModel',
    method: 'post',
    data: data
  })
}

// 修改设备型号信息
export function updateDevModel(data) {
  return request({
    url: '/buss/devModel',
    method: 'put',
    data: data
  })
}

// 删除设备型号信息
export function delDevModel(id) {
  return request({
    url: '/buss/devModel/' + id,
    method: 'delete'
  })
}

// 设备型号状态修改
export function changeDevModelStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/buss/devModel/changeStatus',
    method: 'put',
    data: data
  })
}
