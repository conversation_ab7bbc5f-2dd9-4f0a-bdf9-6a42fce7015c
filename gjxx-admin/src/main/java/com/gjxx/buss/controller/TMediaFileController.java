package com.gjxx.buss.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.gjxx.buss.domain.TCarDev;
import com.gjxx.buss.service.ITCarDevService;
import com.gjxx.common.config.MinioConfig;
import com.gjxx.common.core.domain.R;
import com.gjxx.common.utils.old.ConvertUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.gjxx.common.annotation.Log;
import com.gjxx.common.core.controller.BaseController;
import com.gjxx.common.enums.BusinessType;
import com.gjxx.buss.domain.TMediaFile;
import com.gjxx.buss.service.ITMediaFileService;
import com.gjxx.common.utils.poi.ExcelUtil;
import com.gjxx.common.core.page.TableDataInfo;

/**
 * 多媒体文件信息Controller
 *
 * <AUTHOR>
 * @date 2024-07-08
 */
@RestController
@RequestMapping("/buss/mediaFile")
public class TMediaFileController extends BaseController {
    @Autowired
    private ITMediaFileService tMediaFileService;
    @Autowired
    private MinioConfig minioConfig;
    @Autowired
    private ITCarDevService tCarDevService;

    /**
     * 查询多媒体文件信息列表
     */
    @PreAuthorize("@ss.hasPermi('buss:mediaFile:list')")
    @GetMapping("/list")
    public TableDataInfo list(TMediaFile tMediaFile) {
        startPage();
        List<TMediaFile> list = tMediaFileService.selectTMediaFileList(tMediaFile);
        for(TMediaFile mediaFile:list){
            if (mediaFile.getFilePath() != null) {
                mediaFile.setFilePath(minioConfig.getBaseUrl() + mediaFile.getFilePath());
            }
        }
        return getDataTable(list);
    }

    /**
     * 导出多媒体文件信息列表
     */
    @PreAuthorize("@ss.hasPermi('buss:mediaFile:export')")
    @Log(title = "多媒体文件信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TMediaFile tMediaFile) {
        ExcelUtil<TMediaFile> util = new ExcelUtil<>(TMediaFile.class);

        List<TMediaFile> list = tMediaFileService.selectTMediaFileList(tMediaFile);
        util.exportExcel(response, list, "多媒体文件信息数据");
    }

    /**
     * 获取多媒体文件信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('buss:mediaFile:query')")
    @GetMapping(value = "/{id}")
    public R<TMediaFile> getInfo(@PathVariable("id") Long id) {
        return R.ok(tMediaFileService.getById(id));
    }

    /**
     * 新增多媒体文件信息
     */
    @PreAuthorize("@ss.hasPermi('buss:mediaFile:add')")
    @Log(title = "多媒体文件信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody TMediaFile tMediaFile) {
        return R.ok(tMediaFileService.save(tMediaFile));
    }

    /**
     * 修改多媒体文件信息
     */
    @PreAuthorize("@ss.hasPermi('buss:mediaFile:edit')")
    @Log(title = "多媒体文件信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody TMediaFile tMediaFile) {
        return R.ok(tMediaFileService.updateById(tMediaFile));
    }

    /**
     * 删除多媒体文件信息
     */
    @PreAuthorize("@ss.hasPermi('buss:mediaFile:remove')")
    @Log(title = "多媒体文件信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        return R.ok(tMediaFileService.removeBatchByIds(CollectionUtils.arrayToList(ids)));
    }
}
