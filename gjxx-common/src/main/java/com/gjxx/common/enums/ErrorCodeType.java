package com.gjxx.common.enums;

import java.io.Serializable;

/**
 */

public enum ErrorCodeType implements IEnum<Integer>, Serializable {

    SUCCESS(0, "操作成功"),
    P_FAILURE(1, "操作失败"),
    P_PARAM_ERROR(2, "参数错误"),
    P_SERVICE_NOT_ACCESS(3, "服务未开通"),
    P_CETIFICATE_ERROR(4, "公钥证书未配置"),
    P_PARTNER_VALIDATION_OVERDUE(101, "token令牌过期"),
    P_PARTNER_CERTIFICATE_FAILURE(102, "证书验签失败"),
    P_PARTNER_USERNAME_PASSWORD_MISMATCH(103, "认证用户名或密码错误"),
    P_PARTNER_IP_NO_ACCESS(104, "ip地址禁止访问"),
    P_PARTNER_DEVINFO_RECORD(105, "调用监管平台设备备案失败"),
    P_PARTNER_TRAINIMGINFO(106, "调用监管平台实车培训过程图片上报失败"),
    P_PARTNER_IMAGEUP(107, "调用监管平台文件传输失败"),
    P_PARTNER_BUSSSCHINFO_RECORD(108, "调用监管平台机构备案失败"),
    P_VALIDATIONFAILURE(109, "验证失败"),
    P_PARTNER_CLASSRECORDSELECT(110, "调用监管平台汇总有效学时查询失败"),
    P_DEV_REGISTER(111, "设备Imei已经注册过");

    private Integer value;
    private String msg;

    ErrorCodeType(Integer value, String msg) {
        this.value = value;
        this.msg = msg;
    }


    @Override
    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}
