package com.gjxx.buss.domain.bean;

import lombok.Data;

import java.io.Serializable;

@Data
public class ParamSetDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 选择的车辆设备ID
     */
    private String selectCarDevId;
    /**
     * 选择的车辆设备sim卡号
     */
    private String simNo;
    /**
     * 位置汇报策略
     */
    private String locationStrategy;
    /**
     * 位置汇报方案
     */
    private String locationProject;
    /**
     * 心跳发送间隔
     */
    private String heartInterval;
    /**
     * 休眠汇报间隔
     */
    private String dormancyInterval;
    /**
     * 紧急报警汇报间隔
     */
    private String urgentWarningInterval;
    /**
     * 缺省汇报间隔
     */
    private String defaultInterval;
}
