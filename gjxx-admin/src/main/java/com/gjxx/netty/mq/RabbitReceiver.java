package com.gjxx.netty.mq;

import com.gjxx.netty.runnable.MediaFileRunnable;
import com.gjxx.netty.runnable.TrajectoryRunnable;
import com.gjxx.netty.runnable.WarningRunnable;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;


@Slf4j
@Component
public class RabbitReceiver {
	/**
	 * 处理反馈信息线程池
	 * corePoolSize 核心池大小corePoolSize：表示线程池维护线程的最少数量
	 * maximumPoolSize 线程池最大数量：表示线程池维护线程的最大数量
	 * keepAliveTime 线程池维护线程所允许的空闲时间：表示线程池维护线程所允许的空闲时间
	 * unit keepAliveTimeUnit：表示线程池维护线程所允许的空闲时间的单位
	 * workQueue workQueue：表示线程池所使用的缓冲队列
	 * threadFactory threadFactory：表示线程池创建线程的工厂
	 * rejectedExecutionHandler rejectedExecutionHandler：表示线程池对拒绝任务的处理策略
	 */
	private static ExecutorService threadPool = new ThreadPoolExecutor(10, 20, 60,
			TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(80), Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy());

	@RabbitHandler
	@RabbitListener(queues = {RabbitConfig.trajectory_queue})
	public void trajectoryProcess(String message) {
		try {
			log.info("轨迹定位消息队列消费");
			threadPool.execute(new TrajectoryRunnable(message));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@RabbitHandler
	@RabbitListener(queues = {RabbitConfig.mediafile_queue})
	public void mediaFileProcess(String message) {
		try {
			log.info("多媒体文件消息队列消费");
			threadPool.execute(new MediaFileRunnable(message));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@RabbitHandler
	@RabbitListener(queues = {RabbitConfig.warning_queue})
	public void warningProcess(String message) {
		try {
			log.info("报警信息消息队列消费");
			threadPool.execute(new WarningRunnable(message));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
