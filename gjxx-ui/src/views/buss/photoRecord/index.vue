<template>
  <div class="video-container">
    <div class="video-left-board">
      <div class="video-left-board-top">
        <div style="padding-top: 2px;padding-left:5px;">
          <el-input
            v-model="queryParams.vehicleId"
            placeholder="可查询车辆标识"
            clearable
            style="width: 76%;"
            @clear="handleQuery"/>
          <el-button  style="" icon="el-icon-search"  @click="handleQuery"></el-button>
        </div>
      </div>
      <div class="video-left-board-down">
        <el-tree
          ref="myTree"
          :data="treeData"
          :props="props"
          node-key="id"
          :highlight-current="true"
          :load="loadNode"
          :render-content="renderContent"
          lazy
          @node-click="handleNodeClick">
        </el-tree>
      </div>
    </div>

    <div class="video-center-board">
      <div class="app-container">
        <el-form :model="photoQueryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="SIM卡号" prop="simNo">
            <el-input
              v-model="photoQueryParams.simNo"
              placeholder="请输入SIM卡号"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"/>
          </el-form-item>
          <el-form-item label="视频通道" prop="vedioCh">
            <el-select
              v-model="photoQueryParams.vedioCh"
              placeholder="视频通道"
              clearable
              style="width: 240px">
              <el-option
                v-for="dict in dict.type.t_vedio_ch"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="photoHandleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table ref="tables" v-loading="loading" :data="photoRecordList" :default-sort="defaultSort" @sort-change="handleSortChange">
          <el-table-column label="车辆标识" align="center" prop="vehicleId" />
          <el-table-column label="SIM卡号" align="center" prop="simNo" />
          <el-table-column label="视频通道" align="center" prop="vedioCh">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.t_vedio_ch" :value="scope.row.vedioCh"/>
            </template>
          </el-table-column>
          <el-table-column label="照片" align="center" prop="filePath"  >
            <template slot-scope="scope">
              <el-image
                style="width: 95px; height: 95px"
                :src="scope.row.filePath"
                :preview-src-list="[scope.row.filePath]">
              </el-image>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime"   sortable="custom" :sort-orders="['descending', 'ascending']">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作"   align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleView(scope.row,scope.index)"
                v-hasPermi="['buss:carDev:query']">详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="photoQueryParams.pageNum"
          :limit.sync="photoQueryParams.pageSize"
          @pagination="getListPhotoRecord"/>

        <!-- 图片信息详情对话框 -->
        <el-dialog title="图片信息详情" :visible.sync="details" width="60%" append-to-body>
          <el-form ref="form" :model="form" label-width="180px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="车辆标识：">{{ form.vehicleId }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="SIM卡号：">{{ form.simNo }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="照片：" >
                  <template slot-scope="scope">
                    <img :src="form.filePath" width="100" height="100" />
                  </template>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="视频通道：">{{ form.vedioCh }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="创建时间：">{{ parseTime(form.createTime) }}</el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="details = false">关 闭</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>

import {getCarDevTreeNoCHn,getCarDevTreeNoCHnByVehicleId} from "@/api/buss/realTimeGps";
import {listPhotoRecord} from "@/api/buss/photoRecord";

export default {
  name: "PhotoRecord",
  dicts: ['t_vedio_ch'],
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 图片表格数据
      photoRecordList: null,
      // 是否显示弹出层
      details: false,
      // 表单参数
      form: {},
      // 默认排序
      defaultSort: {prop: 'createTime', order: 'descending'},
      // 车辆设备树
      props: {
        id: 'id',
        carDevId: 'carDevId',
        label: 'label',
        carDevStatus: 'carDevStatus',
        whetherCompanyNode: 'whetherCompanyNode',
        whetherCarDevNode: 'whetherCarDevNode',
        whetherCarDevChnNode: 'whetherCarDevChnNode',
        isLeaf: 'whetherLastLevelNode'
      },
      renderContent(h, { node, data }) {
        if(data.whetherCarDevNode){
          if("01" === data.carDevStatus){
            return (
              <span>
              <svg-icon icon-class="car-online"/>
              <span style="margin-left:3px">{node.label}</span>
            </span>);
          }else {
            return (
              <span>
              <svg-icon icon-class="car-offline"/>
              <span style="margin-left:3px">{node.label}</span>
            </span>);
          }
        }else {
          return (
            <span>
              <i class={node.icon}/>
              <span>{node.label}</span>
            </span>);
        }
      },
      propParams: {
        id: null,
        yesNotCarDevNode: null
      },
      selectCarDevId: null,//选择的车辆设备id
      // 查询参数
      queryParams: {
        vehicleId: null
      },
      // 查询参数
      photoQueryParams: {
        pageNum: 1,
        pageSize: 5,
        selectCarDevId: undefined,
        simNo: undefined,
        vedioCh: undefined
      },
      // 日期范围
      dateRange: [],
      treeData: []
    }
  },
  watch: {
  },
  created() {
    this.handleSortChange(this.defaultSort);
  },
  methods: {
    loadNode(node, resolve) {
      if (node === null || node === undefined || node.level === 0){
        this.propParams.id = null;
        this.propParams.whetherCarDevNode = false;
        getCarDevTreeNoCHn(this.propParams).then(response => {
          this.treeData = response.data;
        });
      }else{
        if (node.data.isLeaf === true){
          return resolve([]);
        }
        this.propParams.id = node.data.id;
        this.propParams.whetherCarDevNode = node.data.whetherCarDevNode;
        this.propParams.whetherCompanyNode = node.data.whetherCompanyNode;

        getCarDevTreeNoCHn(this.propParams).then(response => {
          if(node.data.whetherCompanyNode){
            var label  = node.data.label;
            var label1 = label.split("(")[0];
            var label2 = response.data[0].onlineStatusCount;
            node.data.label = label1+label2;
          }
          return resolve(response.data);
        });
      }
    },
    viewImage(photoUrl) {
      window.open(photoUrl);
    },
    // 动态取值排序
    handleSortChange(column) {
      this.photoQueryParams.orderByColumn = column.prop;//查询字段是表格中字段名字
      this.photoQueryParams.isAsc = column.order;//动态取值排序顺序
      this.getListPhotoRecord();
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.details = true;
      this.form = row;
    },
    /** 查询图片记录列表 */
    getListPhotoRecord() {
      console.log("this.photoQueryParams.selectCarDevId===="+this.photoQueryParams.selectCarDevId);
      this.loading = true;
      listPhotoRecord(this.addDateRange(this.photoQueryParams, this.dateRange)).then(response => {
          this.photoRecordList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 搜索按钮操作 */
    photoHandleQuery() {
      this.photoQueryParams.pageNum = 1;
      this.getListPhotoRecord();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.photoQueryParams.selectCarDevId = undefined;
      this.$refs.myTree.setCurrentKey(null);
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
      this.photoHandleQuery();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if(this.queryParams.vehicleId === null || this.queryParams.vehicleId === ''|| this.queryParams.vehicleId.trim() === ''){
        this.loadNode();
      }else {
        getCarDevTreeNoCHnByVehicleId(this.queryParams).then(response => {
          this.treeData = response.data;
        });
      }
    },
    // 节点单击事件
    handleNodeClick(data) {
      console.log(data);
      const tree = this.$refs.myTree;
      if(data.whetherCarDevNode){
        this.selectCarDevId = data.id;
        this.photoQueryParams.selectCarDevId = data.id;
        if (tree) {
          tree.setCurrentKey(data.id);
        }
        this.getListPhotoRecord();
      }else {
        tree.setCurrentKey(null);
        this.photoQueryParams.selectCarDevId = undefined;
      }
    },
  }
}
</script>

<style lang='scss' scoped>

.video-button{
  background-color: #e6a23c;
  color: white;
  height: 36px;
  margin-left: 10px
}
.video-select{
  width: 130px;
  margin-left:10px;
  font-size: 12px;
}
.video-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 84px);
}

.video-left-board {
  width: 260px;
  position: absolute;
  left: 0;
  top: 0;
  height: calc(100vh - 84px);
}

.video-left-board-top{
  background-color: #E8EAED;
  position: relative;
  height: 42px;
  //background: #fff;
  border-bottom: 1px solid rgb(214, 214, 214);
  border-right: 1px solid rgb(214, 214, 214);
  box-sizing: border-box;
}

.video-left-board-down{
  border-right: 1px solid rgb(214, 214, 214);
  overflow: auto;
  position: relative;
  height: calc(100vh - 126px);
  background: #fff;
  box-sizing: border-box;
}
/* 设置::-webkit-scrollbar可以让滚动条消失，但仍可以滚动 */
//.video-left-board-down::-webkit-scrollbar {
//  display:none
//}
.video-center-board {
  overflow: auto;
  height: calc(100vh - 84px);
  width: auto;
  margin: 0 0 0 260px;
  box-sizing: border-box;
}
</style>
