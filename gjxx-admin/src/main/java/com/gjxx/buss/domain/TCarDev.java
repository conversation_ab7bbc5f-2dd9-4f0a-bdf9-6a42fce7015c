package com.gjxx.buss.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.gjxx.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gjxx.common.core.domain.MpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 车辆设备信息对象 t_car_dev
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName("t_car_dev")
@ApiModel(value = "车辆设备信息")
public class TCarDev extends MpBaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty("ID")
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 车辆标识 */
    @Excel(name = "车辆标识")
    @ApiModelProperty("车辆标识")
    private String vehicleId;

    /** 车牌颜色 */
    @Excel(name = "车牌颜色")
    @ApiModelProperty("车牌颜色")
    private String vehicleNoColor;

    /** SIM卡号 */
    @Excel(name = "SIM卡号")
    @ApiModelProperty("SIM卡号")
    private String simNo;

    /** 隶属车队ID */
    @Excel(name = "隶属车队ID")
    @ApiModelProperty("隶属车队ID")
    private Long carTeamId;

    /** 设备型号ID */
    @Excel(name = "设备型号ID")
    @ApiModelProperty("设备型号ID")
    private Long devModelId;

    /** 视频通道 */
    @Excel(name = "视频通道")
    @ApiModelProperty("视频通道")
    private String vedioCh;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private String createUser;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /** 修改人 */
    @Excel(name = "修改人")
    @ApiModelProperty("修改人")
    private String updateUser;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 视频通道列表 */
    @TableField(exist = false)
    private List<String> vedioChList = new ArrayList<>();

}
