# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /

publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIdU88QtjXHhCTWrT52hNvfrRr5kPExgUu52wYc+/XrZNDd/IXQTvayKJ6NpO9NYtLsSGmVHrBPbxOxZYvvK788CAwEAAQ==
# Spring配置
spring:
  # rabbitmq 配置
  rabbitmq:
    host: 127.0.0.1
    port: 9602
    username: admin
    password: Gjxx@123456
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6350
    # 数据库索引
    database: 4
    # 密码
    password: capital/123456
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: **************************************************************************************************************************************************
        username: root
        password: HUD0BOXOUG9APqFRjxjbz6iQwe+/c1LUxtNpI0ZQJ8aoleNEfA4oxKrLM+RS+qiNJAhu5cepDH6L9SS+u7rh8g==
        connection-properties: config.decrypt=true;config.decrypt.key=${publicKey}
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: gjxx
        login-password: Gps@123456

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /prod-api

# Minio配置
minio:
  url: http://127.0.0.1:62221
  accessKey: minio
  secretKey: Sim@123456
  bucketName: gps
  baseUrl: http://127.0.0.1:62221
