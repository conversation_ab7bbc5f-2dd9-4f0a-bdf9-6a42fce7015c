<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />
    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}" />

    <!--控制台日志输出配置-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <!--对输出日志进行格式化-->
        <encoder>
            <!-- %d日期,%p日志级别,%file文件名,%line所在行数,%m输出的信息,%n换行 -->
            <!--<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>-->
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <!--字符编码-->
            <charset>UTF-8</charset><!--此处设置字符集-->
        </encoder>
    </appender>

    <springProperty name="LOG_NAME" source="logging.log-name" defaultValue="log"/>
    <springProperty name="LOG_PATH" source="logging.log-path" defaultValue="/log/gjxx-system"/>
    <springProperty name="LOG_PATTERN" source="logging.log-pattern" defaultValue="%d{yyyy-MM-dd_HH}"/>
    <springProperty name="LOG_MAX_HISTORY" source="logging.log-max-history" defaultValue="72"/>
    <springProperty name="LOG_TOTAL_SIZE" source="logging.log-total-size" defaultValue="5GB"/>
    <springProperty name="LOG_LEVEL" source="logging.log-level" defaultValue="info"/>

    <!--日志文件配置-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--被写入的文件名，可以是相对目录，也可以是绝对目录，如果上级目录不存在会自动创建，没有默认值。-->
        <!--滚动记录文件：根据时间来制定滚动策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${LOG_NAME}.${LOG_PATTERN}.log</fileNamePattern>
            <maxHistory>${LOG_MAX_HISTORY}</maxHistory>
            <totalSizeCap>${LOG_TOTAL_SIZE}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
    </appender>

    <!--打印sql-->
    <logger name="com.gjxx" level="DEBUG" />

    <!--指定日志输出等级-->
    <root level="${LOG_LEVEL}">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </root>

    <root level="${LOG_LEVEL}">
        <appender-ref ref="file"/>
    </root>
</configuration>
