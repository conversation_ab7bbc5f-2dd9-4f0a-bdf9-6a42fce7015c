package com.gjxx.buss.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gjxx.buss.domain.TCarDev;
import com.gjxx.buss.domain.TCarTeam;
import com.gjxx.buss.domain.TDevModel;
import com.gjxx.buss.service.*;
import com.gjxx.common.constant.CacheConstants;
import com.gjxx.common.core.domain.R;
import com.gjxx.common.core.redis.RedisCache;
import com.gjxx.common.tcp.*;
import com.gjxx.mongo.entity.CarDevOnlineStatus;
import com.gjxx.mongo.service.CarDevOnlineStatusService;
import com.gjxx.netty.MyDecoder;
import com.gjxx.netty.MyEncoder;
import com.gjxx.netty.codec.AESCription;
import com.gjxx.netty.codec.BitOperator;
import com.gjxx.netty.codec.DateUtil;
import com.gjxx.netty.codec.TPMSConsts;
import com.gjxx.netty.mq.RabbitConfig;
import com.gjxx.netty.req.*;
import com.gjxx.netty.resp.ServerCommonRespMsgBody;
import com.gjxx.netty.resp.TerminalRegisterMsgRespBody;
import com.gjxx.netty.session.Session;
import com.gjxx.netty.session.SessionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 终端调用上行服务
 * <AUTHOR>
 * @date 2018/06/04
 */
@Slf4j
@Component
@Service
public class TerminalMsgProcessUpServiceImpl extends BaseMsgProcessService implements TerminalMsgProcessUpService {

    @Autowired
    private ITCarDevService tCarDevService;
    @Autowired
    private ITCarTeamService tCarTeamService;
    @Autowired
    private ITDevModelService tDevModelService;
    @Autowired
    private AmqpTemplate rabbitTemplate;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private CarDevOnlineStatusService carDevOnlineStatusService;

    private BitOperator bitOperator = new BitOperator();
    private MyEncoder msgEncoder = new MyEncoder();
    private MyDecoder msgDecoder = new MyDecoder();
    private SessionManager sessionManager = SessionManager.getInstance();

    @Override
    public void processRegisterMsg(TerminalRegisterMsg msg) throws Exception {
        log.info("【客户端上报-终端注册】:{}", JSON.toJSONString(msg, JSONWriter.Feature.PrettyFormat));
        TerminalRegisterMsgRespBody respMsgBody = new TerminalRegisterMsgRespBody();
        String phone = msg.getMsgHeader().getTerminalPhone();//终端手机号
        //获取终端注册消息对象
        TerminalRegisterMsg  terminalRegisterMsg = this.msgDecoder.toTerminalRegisterMsg(msg);
        String licensePlate = terminalRegisterMsg.getTerminalRegInfo().getLicensePlate();//车辆标识
        String terminalType = terminalRegisterMsg.getTerminalRegInfo().getTerminalType();//终端型号
        List<TCarDev> tCarDevList = tCarDevService.list(new QueryWrapper<TCarDev>().
                eq("vehicle_id", licensePlate));
        if(tCarDevList.size() <= 0){
            respMsgBody.setReplyCode(TerminalRegisterMsgRespBody.car_not_found);
            log.info("客户端上报-终端注册失败,根据车辆标识未查询到车辆设备,终端手机号:{},车辆标识:{},终端型号:{}"
                    ,phone,licensePlate,terminalType);
        }else{
            TCarDev tCarDev = tCarDevList.get(0);
            TDevModel tDevModel = tDevModelService.getById(tCarDev.getDevModelId());
            if(!terminalType.equals(tDevModel.getDevModel())){
                respMsgBody.setReplyCode(TerminalRegisterMsgRespBody.car_not_found);
                log.info("客户端上报-终端注册失败,上报的终端型号与车辆标识不匹配,终端手机号:{},车辆标识:{},终端型号:{}"
                        ,phone,licensePlate,terminalType);
            }
            if(!phone.equals(tCarDev.getSimNo())){
                respMsgBody.setReplyCode(TerminalRegisterMsgRespBody.terminal_not_found);
                log.info("客户端上报-终端注册失败,上报的终端手机号与车辆标识不匹配,终端手机号:{},车辆标识:{},终端型号:{}"
                        ,phone,licensePlate,terminalType);
            }else{
                final String sessionId = Session.buildId(msg.getChannel());
                Session session = sessionManager.findBySessionId(sessionId);
                if (session == null) {
                    session = Session.buildSession(msg.getChannel(), phone);
                }
                session.setAuthenticated(true);
                session.setTerminalPhone(phone);
                sessionManager.put(session.getId(), session);
                respMsgBody.setReplyCode(TerminalRegisterMsgRespBody.success);
                respMsgBody.setReplyToken(AESCription.encrypt(tCarDev.getSimNo(), AESCription.AES_KEY));//鉴权码
            }
        }
        respMsgBody.setReplyFlowId(msg.getMsgHeader().getFlowId());
        int flowId = super.getFlowId(msg.getChannel());
        byte[] bs = this.msgEncoder.encode4TerminalRegisterResp(msg, respMsgBody, flowId);
        //向终端发送数据
        super.send2Client(msg.getChannel(), bs,"回复客户端-终端注册");
    }

    @Override
    public void processAuthMsg(TerminalAuthenticationMsg msg) throws Exception {
        log.info("【客户端上报-终端鉴权】:{}", JSON.toJSONString(msg,JSONWriter.Feature.PrettyFormat));
        String phone = msg.getMsgHeader().getTerminalPhone();
        ServerCommonRespMsgBody respMsgBody = new ServerCommonRespMsgBody();
        if (msg.getCheckSum() != msg.getCalculatedCheckSum()) {
            respMsgBody.setReplyCode(ServerCommonRespMsgBody.msg_error);
            log.info("客户端上报-终端鉴权失败,校验码不正确,终端手机号：{}"+phone);
        }else{
            String token = msg.getAuthCode();//鉴权码
            List<TCarDev> tCarDevList = tCarDevService.list(new QueryWrapper<TCarDev>().
                    eq("sim_no", phone));
            if(tCarDevList.size() <= 0){
                respMsgBody.setReplyCode(ServerCommonRespMsgBody.failure);
                log.info("客户端上报-终端鉴权失败,根据终端手机号未查询到车辆设备,终端手机号:{}",phone);
            }else {
                String tokenPhone = AESCription.encrypt(phone, AESCription.AES_KEY);
                if(!tokenPhone.equals(token)){
                    respMsgBody.setReplyCode(ServerCommonRespMsgBody.failure);
                    log.info("客户端上报-终端鉴权失败,鉴权码不正确,终端手机号:{}",phone);
                }else {
                    final String sessionId = Session.buildId(msg.getChannel());
                    Session session = sessionManager.findBySessionId(sessionId);
                    if (session == null) {
                        session = Session.buildSession(msg.getChannel(), msg.getMsgHeader().getTerminalPhone());
                    }
                    session.setAuthenticated(true);
                    session.setTerminalPhone(msg.getMsgHeader().getTerminalPhone());
                    sessionManager.put(session.getId(), session);
                    CarDevOnlineStatus carDevOnlineStatus = carDevOnlineStatusService.findCarDevOnlineStatusById(phone);
                    if(carDevOnlineStatus == null){
                        TCarDev tCarDev = tCarDevList.get(0);
                        carDevOnlineStatus = new CarDevOnlineStatus();
                        carDevOnlineStatus.setId(phone);
                        carDevOnlineStatus.setVehicleId(tCarDev.getVehicleId());
                        carDevOnlineStatus.setVedioCh(tCarDev.getVedioCh());
                        TCarTeam tCarTeam = tCarTeamService.getById(tCarDev.getCarTeamId());
                        if(tCarTeam != null){
                            carDevOnlineStatus.setCarTeamName(tCarTeam.getName());
                        }
                        carDevOnlineStatus.setCarTeamId(tCarDev.getCarTeamId());
                        carDevOnlineStatusService.save(carDevOnlineStatus);
                    }
                    redisCache.setCacheObject(CacheConstants.CAR_DEV_ONLINE_STATUS + phone, "online");
                    respMsgBody.setReplyCode(ServerCommonRespMsgBody.success);
                }
            }
        }
        respMsgBody.setReplyFlowId(msg.getMsgHeader().getFlowId());
        respMsgBody.setReplyId(msg.getMsgHeader().getMsgId());
        int flowId = super.getFlowId(msg.getChannel());
        byte[] bs = this.msgEncoder.encode4ServerCommonRespMsg(msg, respMsgBody, flowId);
        super.send2Client(msg.getChannel(), bs,"回复客户端-终端鉴权");
    }

    @Override
    public void processTerminalHeartBeatMsg(PackageData req) throws Exception {
        ServerCommonRespMsgBody respMsgBody = new ServerCommonRespMsgBody();
        respMsgBody.setReplyFlowId(req.getMsgHeader().getFlowId());
        respMsgBody.setReplyId(req.getMsgHeader().getMsgId());
        if (req.getCheckSum() != req.getCalculatedCheckSum()) {
            respMsgBody.setReplyCode(ServerCommonRespMsgBody.msg_error);
        }else{
            String phone = req.getMsgHeader().getTerminalPhone();
            Session session = sessionManager.findByTerminalPhone(phone);
            if(session == null){
                respMsgBody.setReplyCode(ServerCommonRespMsgBody.failure);
            }else{

            }
        }
        int flowId = super.getFlowId(req.getChannel());
        byte[] bs = this.msgEncoder.encode4ServerCommonRespMsg(req, respMsgBody, flowId);
        super.send2Client(req.getChannel(), bs,"回复客户端-心跳信息");
    }

    @Override
    public void processTerminalLogoutMsg(PackageData req) throws Exception {
        log.info("【客户端上报-终端注销】:{}", JSON.toJSONString(req,JSONWriter.Feature.PrettyFormat));
        ServerCommonRespMsgBody respMsgBody = new ServerCommonRespMsgBody();
        respMsgBody.setReplyFlowId(req.getMsgHeader().getFlowId());
        respMsgBody.setReplyId(req.getMsgHeader().getMsgId());
        if (req.getCheckSum() != req.getCalculatedCheckSum()) {
            respMsgBody.setReplyCode(ServerCommonRespMsgBody.msg_error);
        }else{
            String phone = req.getMsgHeader().getTerminalPhone();
            Session session = sessionManager.findByTerminalPhone(phone);
            if(session == null){
                respMsgBody.setReplyCode(ServerCommonRespMsgBody.failure);
            }else{
                redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + phone);
                carDevOnlineStatusService.delete(phone);
                this.sessionManager.removeBySessionId(session.getId());
                respMsgBody.setReplyCode(ServerCommonRespMsgBody.success);
            }
        }
        int flowId = super.getFlowId(req.getChannel());
        byte[] bs = this.msgEncoder.encode4ServerCommonRespMsg(req, respMsgBody, flowId);
        super.send2Client(req.getChannel(), bs,"回复客户端-终端注销");
    }

    @Override
    public void processLocationInfoUploadMsg(LocationInfoUploadMsg req) throws Exception {
//        log.warn("位置信息上报:{}", JSON.toJSONString(req, true));
        ServerCommonRespMsgBody respMsgBody = new ServerCommonRespMsgBody();
        respMsgBody.setReplyFlowId(req.getMsgHeader().getFlowId());
        respMsgBody.setReplyId(req.getMsgHeader().getMsgId());
        if (req.getCheckSum() != req.getCalculatedCheckSum()) {
            respMsgBody.setReplyCode(ServerCommonRespMsgBody.msg_error);
        }else{
            String phone = req.getMsgHeader().getTerminalPhone();
            Session session = sessionManager.findByTerminalPhone(phone);
            if(session == null){
                redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + phone);
                carDevOnlineStatusService.delete(phone);
                respMsgBody.setReplyCode(ServerCommonRespMsgBody.failure);
            }else{
                List<TCarDev> tCarDevList = tCarDevService.list(new QueryWrapper<TCarDev>().eq("sim_no", phone));
                if(tCarDevList.size() <= 0){
                    respMsgBody.setReplyCode(ServerCommonRespMsgBody.failure);
                }else{
                    String latStr = String.valueOf(req.getLatitude());
                    if(latStr.length() > 6){
                        latStr = latStr.substring(0,latStr.length()-6)+"."+latStr.substring(latStr.length()-6);
                    }
                    String lngStr = String.valueOf(req.getLongitude());
                    if(lngStr.length() > 6){
                        lngStr = lngStr.substring(0,lngStr.length()-6)+"."+lngStr.substring(lngStr.length()-6);
                    }
                    CarDevOnlineStatus carDevOnlineStatus = carDevOnlineStatusService.findCarDevOnlineStatusById(phone);
                    if(carDevOnlineStatus != null){
                        if(req.getStatusField() != null&& req.getStatusField().length() == 32){
                            String accStatus = req.getStatusField().substring(31,32);
                            if("1".equals(accStatus)){
                                carDevOnlineStatus.setAccStatus("01");//01-开
                            }else {
                                carDevOnlineStatus.setAccStatus("02");//02-关
                            }
                        }
                        carDevOnlineStatus.setLongitude(lngStr);
                        carDevOnlineStatus.setLatitude(latStr);
                        carDevOnlineStatus.setDirection(req.getDirection());
                        try {
                            Date date = DateUtil.getDate(req.getTrajectoryTime(),"yyMMddHHmmss");
                            carDevOnlineStatus.setPosTime(DateUtil.getDateString(date));
                        } catch (ParseException e) {
                            e.printStackTrace();
                            log.error("位置信息上报--定位日期转换错误"+e.getMessage());
                        }
                        this.rabbitTemplate.convertAndSend(RabbitConfig.trajectory_queue, JSON.toJSONString(req));
                        if(!"00000000000000000000000000000000".equals(req.getWarningFlagField())){
                            this.rabbitTemplate.convertAndSend(RabbitConfig.warning_queue, JSON.toJSONString(req));
                            carDevOnlineStatus.setWarningStatus("01");//01-报警
                        }else {
                            carDevOnlineStatus.setWarningStatus("02");//02-未报警
                        }
                        carDevOnlineStatusService.save(carDevOnlineStatus);
                        respMsgBody.setReplyCode(ServerCommonRespMsgBody.success);
                    }else {
                        respMsgBody.setReplyCode(ServerCommonRespMsgBody.failure);
                    }
                }
            }
        }
        int flowId = super.getFlowId(req.getChannel());
        byte[] bs = this.msgEncoder.encode4ServerCommonRespMsg(req, respMsgBody, flowId);
        super.send2Client(req.getChannel(), bs,"回复客户端-位置信息上报");
    }

    @Override
    public void locationInfoQueryMsg(LocationInfoQueryMsg req) throws Exception {
        log.info("【客户端上报-位置信息查询应答】:{}", JSON.toJSONString(req,JSONWriter.Feature.PrettyFormat));
        if (req.getCheckSum() != req.getCalculatedCheckSum()) {
            log.error("校验码不正确");
        }else{
            String phone = req.getMsgHeader().getTerminalPhone();
            Session session = sessionManager.findByTerminalPhone(phone);
            if(session == null){
                log.error("终端与服务器的连接不存在");
            }else{
                int flowId = req.getMsgFlowId();
                String keyStr = TPMSConsts.cmd_location_query +String.valueOf(flowId);
                redisCache.setCacheObject(keyStr,JSON.toJSONString(req), 60, TimeUnit.SECONDS);
            }
        }
    }

    @Override
    public void photoScreenMsg(PhotoScreenMsg req) throws Exception {
        log.info("【客户端上报-摄像头立即拍摄命令应答】:{}", JSON.toJSONString(req,JSONWriter.Feature.PrettyFormat));
        if (req.getCheckSum() != req.getCalculatedCheckSum()) {
            log.error("校验码不正确");
        }else{
            String phone = req.getMsgHeader().getTerminalPhone();
            Session session = sessionManager.findByTerminalPhone(phone);
            if(session == null){
                log.error("终端与服务器的连接不存在");
            }else{
                int flowId = req.getMsgFlowId();
                String keyStr = String.valueOf(TPMSConsts.cmd_photo_screen)+flowId;
                redisCache.setCacheObject(keyStr,String.valueOf(req.getResult()),60,TimeUnit.SECONDS);
            }
        }
    }

    @Override
    public void mediaFileMsg(PackageData pd) throws Exception {
        String phone = pd.getMsgHeader().getTerminalPhone();
        Session session = sessionManager.findByTerminalPhone(phone);
        if(session == null){
            redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + phone);
            carDevOnlineStatusService.delete(phone);
            log.info("多媒体数据上传--设备不在线");
        }else{
            List<byte[]> result = new ArrayList<>();
            MsgHeader header = pd.getMsgHeader();
            MediaFileMsg mediaFileMsg =  new MediaFileMsg();
            if (header.isHasSubPackage()) {//是否分包
                int total = header.getTotalSubPackage();//消息包总数
                int seq = header.getSubPackageSeq();//包序号
                if (seq == 1) {
                    session.setFileId(Arrays.copyOf(pd.getMsgBodyBytes(), 4));
                    session.setData(new PackageData[total + 1]);
                }
                PackageData[] data = session.getData();
                data[seq] = pd;
                if (checkSubPackage(session.getFileId(), pd, data)) {
                    return;
                }
                for (PackageData item : data) {
                    if (item == null) {
                        continue;
                    }else {
                        if(item.getMsgHeader().getSubPackageSeq() == 1){
                            mediaFileMsg =  msgDecoder.toMediaFileMsg(item);
                        }
                        result.add(item.getMsgBodyBytes());
                    }
                }
                pd.setMsgBodyBytes(this.bitOperator.concatAll(result));
                byte[] dataByte = pd.getMsgBodyBytes();
                byte[] file = new byte[dataByte.length - 36];
                System.arraycopy(dataByte, 36, file, 0, file.length);
                mediaFileMsg.setFile(file);
            }else{
                mediaFileMsg =  msgDecoder.toMediaFileMsg(pd);
            }
            session.setData(null);
            this.rabbitTemplate.convertAndSend(RabbitConfig.mediafile_queue, JSON.toJSONString(mediaFileMsg));
            byte[] msgBody = Arrays.copyOf(pd.getMsgBodyBytes(), 4);
            int flowId = super.getFlowId(pd.getChannel());
            byte[] bs = this.msgEncoder.encodeMediaFileRespMsg(pd, msgBody, flowId);
            super.send2Client(pd.getChannel(), bs,"回复客户端-多媒体数据上传");
        }
    }

    /**
     * 检测是否有漏包
     */
    private boolean checkSubPackage(byte[] fileId, PackageData pd, PackageData[] data) throws Exception {
        int total = pd.getMsgHeader().getTotalSubPackage();
		if (data[total] == null) {
//			byte[] msgBody = this.bitOperator.concatAll(Arrays.asList(//
//					fileId, // 文件ID
//					new byte[] { 0 }
//			));
//            log.error("222222222222222222223");
//			int flowId = super.getFlowId(pd.getChannel());
//			byte[] bs = this.msgEncoder.encodeMediaFileRespMsg(pd, msgBody, flowId);
//			super.send2Client(pd.getChannel(), bs);
			return true;
		} else {
			List<Integer> seqList = new ArrayList<>();
			for (int i = 1; i <= total; i++) {
				if (data[i] == null) {
					seqList.add(i);
				}
			}
			if (seqList.size() > 0) {
				byte[] n = new byte[] { (byte) seqList.size() };// 重传包总数
				List<byte[]> tmpList = new ArrayList<>();// 重传包
				for (int item : seqList) {
					tmpList.add(bitOperator.integerTo2Bytes(item));
                    log.error("item：{}",item);
				}
				byte[] msgBody = this.bitOperator.concatAll(Arrays.asList(//
						fileId, // 文件ID
						n, // 重传包总数
						bitOperator.concatAll(tmpList) // 重传包ID列表
				));
				int flowId = super.getFlowId(pd.getChannel());
				byte[] bs = this.msgEncoder.encodeMediaFileRespMsg(pd, msgBody, flowId);
				super.send2Client(pd.getChannel(), bs,"11111");
				return true;
			}else{
				return false;
			}
		}
    }

    /**
     * 获取文件字节数据
     * @param pd
     * @return
     */
    private byte[] getFile(PackageData pd) {
        if (!pd.getMsgHeader().isHasSubPackage() || pd.getMsgHeader().getSubPackageSeq() == 1) {
            byte[] body = pd.getMsgBodyBytes();
            byte[] file = new byte[body.length - 36];
            System.arraycopy(body, 36, file, 0, file.length);
            return file;
        }
        return pd.getMsgBodyBytes();
    }

    @Override
    public void processTerminalCommonMsg(TerminalCommonMsg req) throws Exception {
        log.info("【客户端上报-通用应答】:{}", JSON.toJSONString(req,JSONWriter.Feature.PrettyFormat));
        if (req.getCheckSum() != req.getCalculatedCheckSum()) {
            log.error("校验码不正确");
        }else{
            String phone = req.getMsgHeader().getTerminalPhone();
            Session session = sessionManager.findByTerminalPhone(phone);
            if(session == null){
                log.error("终端与服务器的连接不存在");
            }else{
                int flowId = req.getTerminalCommonInfo().getRespflowId();
                int respId = req.getTerminalCommonInfo().getRespId();
                int result = req.getTerminalCommonInfo().getResult();
                String keyStr = String.valueOf(respId)+flowId;
                redisCache.setCacheObject(keyStr,String.valueOf(result),30,TimeUnit.SECONDS);
            }
        }
    }

    @Override
    public void historyVideoMsg(PackageData pd) throws Exception {
        String phone = pd.getMsgHeader().getTerminalPhone();
        Session session = sessionManager.findByTerminalPhone(phone);
        if(session == null){
            redisCache.deleteObject(CacheConstants.CAR_DEV_ONLINE_STATUS + phone);
            carDevOnlineStatusService.delete(phone);
        }else{
            List<byte[]> result = new ArrayList<>();
           MsgHeader header = pd.getMsgHeader();
            if (header.isHasSubPackage()) {//是否分包
                int total = header.getTotalSubPackage();//消息包总数
                int seq = header.getSubPackageSeq();//包序号
                if (seq == 1) {
                    session.setFileId(Arrays.copyOf(pd.getMsgBodyBytes(), 4));
                    session.setData(new PackageData[total + 1]);
                }
                PackageData[] data = session.getData();
                log.info("终端上传音视频资源列表--包总数:{}",total);
                log.info("终端上传音视频资源列表--包序号:{}",seq);
                data[seq] = pd;
                if (checkSubPackage(session.getFileId(), pd, data)) {
                    return;
                }
                for (PackageData item : data) {
                    if (item == null) {
                        continue;
                    }else {
                        result.add(item.getMsgBodyBytes());
                    }
                }
                pd.setMsgBodyBytes(this.bitOperator.concatAll(result));
            }
            session.setData(null);
            HistoryVideoMsg historyVideoMsg = msgDecoder.toHistoryVideoMsg(pd);
            log.info("终端上传音视频资源列表:{}", JSON.toJSONString(historyVideoMsg));
            int flowId = historyVideoMsg.getMsgFlowId();
            String keyStr = String.valueOf(TPMSConsts.cmd_history_video)+flowId;
            String historyVideoMsgJson = JSON.toJSONString(historyVideoMsg);
            if(historyVideoMsgJson != null && !"".equals(historyVideoMsgJson)){
                redisCache.setCacheObject(keyStr,historyVideoMsgJson,40,TimeUnit.SECONDS);
            }
        }
    }

    @Override
    public void paramQueryMsg(PackageData pd) throws Exception {
        String phone = pd.getMsgHeader().getTerminalPhone();
        Session session = sessionManager.findByTerminalPhone(phone);
        if(session == null){
            log.error("终端与服务器的连接不存在");
        }else{
            ParamQueryMsg paramQueryMsg = msgDecoder.toParamQueryMsg(pd);
            int flowId = paramQueryMsg.getMsgFlowId();
            String keyStr = String.valueOf(TPMSConsts.cmd_terminal_param_query)+flowId;
            redisCache.setCacheObject(keyStr,JSON.toJSONString(paramQueryMsg),60,TimeUnit.SECONDS);
        }
    }

    @Override
    public void videoAttrUploadMsg(PackageData pd) throws Exception {
        String phone = pd.getMsgHeader().getTerminalPhone();
        Session session = sessionManager.findByTerminalPhone(phone);
        if(session == null){
            log.error("终端与服务器的连接不存在");
        }else{
            VideoAttrMsg videoAttrMsg = msgDecoder.toVideoAttrMsg(pd);
            String keyStr = String.valueOf(TPMSConsts.cmd_terminal_video_attributes)+phone;
//            Jedis jedis = RedisUtil.getJedis();
//            String paramQueryMsgJson = JsonUtils.parseObjToJson(videoAttrMsg);
//            jedis.set(keyStr,paramQueryMsgJson);
//            jedis.expire(keyStr, 60);//60秒有效
//            jedis.close();
        }
    }
}
