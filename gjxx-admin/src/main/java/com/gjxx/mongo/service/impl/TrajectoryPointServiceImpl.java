package com.gjxx.mongo.service.impl;

import com.gjxx.buss.domain.bean.TraceVo;
import com.gjxx.mongo.dao.TrajectoryPointRepository;
import com.gjxx.mongo.entity.TrajectoryPoint;
import com.gjxx.mongo.service.TrajectoryPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TrajectoryPointServiceImpl implements TrajectoryPointService {

    //注入dao
    @Autowired
    private TrajectoryPointRepository trajectoryPointRepository;
    @Autowired
    private MongoTemplate mongoTemplate;


    @Override
    public TrajectoryPoint findTrajectoryPointById(String id) {
        return trajectoryPointRepository.findTrajectoryPointById(id);
    }

    @Override
    public void save(TrajectoryPoint trajectoryPoint) {
        trajectoryPointRepository.save(trajectoryPoint);
    }

    @Override
    public void delete(String id) {
        trajectoryPointRepository.deleteById(id);
    }

    @Override
    public List<TrajectoryPoint> findAllByTime(String startTime,String endTime) {
        Query query = new Query();
        query.addCriteria(Criteria.where("id").lt(endTime).gt(startTime));
        return mongoTemplate.find(query, TrajectoryPoint.class);
    }
}


