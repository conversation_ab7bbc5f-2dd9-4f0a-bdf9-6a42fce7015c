package com.gjxx.buss.domain.bean;


import lombok.Data;

import java.io.Serializable;

/**
 * 车辆设备树结构实体类
 */
@Data
public class CarDevTree implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private String id;
    /**
     * 车辆设备ID
     */
    private Long carDevId;
    /**
     * 节点名称
     */
    private String label;
    /**
     * 车辆设备状态
     */
    private String carDevStatus;
    /**
     * 是否公司节点
     */
    private boolean whetherCompanyNode;
    /**
     * 是否车辆设备节点
     */
    private boolean whetherCarDevNode;
    /**
     * 是否车辆设备通道节点
     */
    private boolean whetherCarDevChnNode;
    /**
     * 是否最小级别节点
     */
    private boolean whetherLastLevelNode;
    /**
     * 在线状统计
     */
    private String onlineStatusCount;
}
