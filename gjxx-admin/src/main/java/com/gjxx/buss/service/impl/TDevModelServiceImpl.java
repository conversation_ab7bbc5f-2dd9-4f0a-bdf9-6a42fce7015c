package com.gjxx.buss.service.impl;

import com.gjxx.common.core.domain.entity.SysRole;
import com.gjxx.common.mp.service.impl.MpServiceImpl;
import java.util.List;

import com.gjxx.common.utils.spring.SpringUtils;
import org.springframework.stereotype.Service;
import com.gjxx.buss.mapper.TDevModelMapper;
import com.gjxx.buss.domain.TDevModel;
import com.gjxx.buss.service.ITDevModelService;

/**
 * 设备型号信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class TDevModelServiceImpl extends MpServiceImpl<TDevModelMapper, TDevModel> implements ITDevModelService {

    /**
     * 查询设备型号信息列表
     *
     * @param tDevModel 设备型号信息
     * @return 设备型号信息
     */
    @Override
    public List<TDevModel> selectTDevModelList(TDevModel tDevModel) {
        return baseMapper.selectTDevModelList(tDevModel);
    }

    @Override
    public List<TDevModel> selectDevModelAll() {
        return SpringUtils.getAopProxy(this).selectTDevModelList(new TDevModel());
    }
}
