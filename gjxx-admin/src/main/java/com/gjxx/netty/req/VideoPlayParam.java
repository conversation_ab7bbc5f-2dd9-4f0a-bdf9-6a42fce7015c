package com.gjxx.netty.req;

import lombok.Data;

import java.io.Serializable;

/**
 * 视频播放消息
 */
@Data
public class VideoPlayParam implements Serializable{
	/**
	 * 车牌号码，采用UTF-8编码
	 */
	private String plateNo;
	/**
	 * 车牌颜色
	 */
	private int plateColor;
	/**
	 * 手机号
	 */
	private String mobileNumber;
	/**
	 * 参数内容对象
	 */
	private  Msg msg;
	/**
	 * token
	 */
	private String token;

	@Data
	public static class Msg implements Serializable{
		/**
		 * 逻辑通道号
		 */
		private int logicalChn;
		/**
		 * 数据类型
		 */
		private int dataType;
		/**
		 * 码流类型
		 */
		private int streamType;
		/**
		 * 视频服务器地址
		 */
		private String sinkIP;
		/**
		 * 视频服务器端口
		 */
		private int sinkPort;
		/**
		 * 控制指令 0：开始回放,1：暂停回放,2：结束回放,3：快进回放,4：关键帧快退回放,5：拖动回放,6：关键帧播放
		 */
		private int ctrlCmd;
		/**
		 * 关闭音视频类型
		 */
		private int closeAVType;
		/**
		 * 切换码流类型
		 */
		private int changeStreamType;
		/**
		 * 丢包率
		 */
		private int lossRate;
		/**
		 * 音视频资源类型
		 */
		private int resourceType;
		/**
		 * 存储器类型
		 */
		private int storageType;
		/**
		 * 回放方式
		 */
		private int replayType;
		/**
		 * 快进或快退倍数
		 */
		private int multiples;
		/**
		 * 开始时间
		 */
		private String beginTime;
		/**
		 * 结束时间
		 */
		private String endTime;
		/**
		 * 拖动回放位置
		 */
		private String replayLocation;
		/**
		 * token
		 */
		private String token;
	}
}
