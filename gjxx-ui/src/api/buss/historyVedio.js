import request from '@/utils/request'
import flv from 'flv.js';

// 历史视频列表查询
export function historyVedioQuery(query) {
  return request({
    url: '/buss/carDev/historyVedioQuery',
    method: 'post',
    params: query
  })
}

// 历史视频播放
export function historyVedioPlay(params) {
  return request({
    url: '/buss/carDev/historyVedioPlay',
    method: 'post',
    params: params
  })
}

// 向终端下发远程录像回放控制
export function historyVedioControl(params) {
  return request({
    url: '/buss/carDev/historyVedioControl',
    method: 'post',
    params: params
  })
}

/**
 * 创建一个FLV播放器，参数如下：
 * container : 视频容器元素
 * muted     : 是否静音
 * url       : HTTP-FLV地址
 */
export function FLVPlayer(opts){
  var videoElement = document.createElement('VIDEO');
  videoElement.autoplay = true;
  videoElement.controls = true;
  videoElement.muted = false;
  videoElement.style.width = '100%';
  videoElement.style.height = '100%';
  opts.container.append(videoElement);
  this.videoElement = videoElement;
  this.httpFlvURL = opts.url;
  this.autoFastForward = opts.autoFastForward;
  this.flvPlayer = null;

  this.play = function(){
    if(this.flvPlayer){
      return;
    }
    const self = this;
    self.flvPlayer = new flv.createPlayer({
      type: 'flv',
      url: self.httpFlvURL,
      // 是否启用 worker，开启后会使用 web worker 解析数据，提高性能
      enableWorker: true,
      // 是否为直播流
      isLive: false,
      // 是否启用媒体源缓存功能
      enableStashBuffer: true,
      // 缓存的初始大小，单位为字节，默认值为 384 KB
      stashInitialSize: 384 * 1024,
      // 缓存起始时间，单位为秒，默认值为 0.8 秒
      stashInitialTime: 0.8,
      // // 是否启用懒加载功能
      // lazyLoad: true,
      // // 最大懒加载时长，单位为秒，默认值为 3 秒
      // lazyLoadMaxDuration: 3,
      // // 是否在打开媒体源之前延迟加载媒体
      // deferLoadAfterSourceOpen: false,
      // // 是否启用 fetch 并行请求
      // // 如果启用，可以提高加载速度，但可能会占用更多的带宽
      // // 当启用时，flv.js 会根据 HTTP Range 头信息来并行请求媒体内容
      // // 如果服务器不支持 Range 头信息，则只能串行请求内容
      // enableRangeRequest: false,
      // // 每次请求的最大并发数
      // // 当 enableRangeRequest 启用时，maxParallelRequest 控制并发数，默认值为 2
      // maxParallelRequest: 2,
      // // 是否启用手动控制缓存大小
      // // 如果开启，需要手动调用 player.bufferringTime 来修改缓存大小
      // enableCustomBaditBuffer: false,
      // 最大缓存时间，单位为秒，默认为 60 秒
      maxBufferingTime: 3600,
      // // 指定每个分片的缓存时长，单位为秒，默认为 10 秒
      // // 如果启用了手动控制缓存，该配置项将失效
      // stashTime: 10,
      // 音频缓存大小，单位为秒，默认值为 0.5 秒
      audioBufferSize: 3600,
      // 视频缓存大小，单位为秒，默认值为 0.5 秒
      videoBufferSize: 3600,
      // // 是否启用优化后的播放器布局逻辑
      // enableOptimizedLayout: false,
      // // 是否启用低延迟模式，仅在 isLive 为 true 时生效
      // lowLatencyMode: false,
      // // 是否开启自适应码率
      // enableAutoQuality: false,
      // // 检测到错误时是否自动重试
      // autoReloadOnError: true,
      // // 是否启用键盘快捷键功能，在页面上通过键盘进行操作
      // enableKeyboard: true,
      // // 是否启用鼠标事件功能，在页面上通过鼠标进行操作
      // enableMouse: true,
      // // 是否启用 Touch 事件功能，在触屏设备上通过手势进行操作
      // enableTouch: true,
      // // 是否启用全屏幕模式
      // enableFullscreen: true,
      // // 是否开启本地日志记录
      // enableStashConsole: false,
      // // 是否开启性能监测功能，默认为 true
      // enablePerformanceMonitor: true,
      // // 自定义 CDN 加速地址
      // segmentsSyncUrlBuilder: null,
      // // 预加载的分片数量，单位为个，默认值为 3 个
      // segmentStartFetchNum: 3,
      // autoCleanupSourceBuffe: true,//是否在销毁播放器实例时清空 MSE 缓存。默认值为 true。
      // autoCleanupMaxBackwardDuration  : 5,
      // autoCleanupMinBackwardDuration  : 1
    });
    self.flvPlayer.attachMediaElement(self.videoElement);
    self.flvPlayer.load();
    self.flvPlayer.play();
  };
  this.destroy = function(){
    this.flvPlayer.destroy();
  }
}


