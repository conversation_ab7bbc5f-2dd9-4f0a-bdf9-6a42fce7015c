package com.gjxx.netty.session;

import lombok.Data;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Data
public class SessionManager {

	private static volatile SessionManager instance = null;
	// netty生成的sessionID和Session的对应关系
	private Map<String, Session> sessionIdMap;
	// 终端手机号和netty生成的sessionID的对应关系
	private Map<String, String> phoneMap;

	public static SessionManager getInstance() {
		if (instance == null) {
			synchronized (SessionManager.class) {
				if (instance == null) {
					instance = new SessionManager();
				}
			}
		}
		return instance;
	}

	public SessionManager() {
		this.sessionIdMap = new ConcurrentHashMap<>();
		this.phoneMap = new ConcurrentHashMap<>();
	}

	public boolean containsKey(String channelId) {
		return sessionIdMap.containsKey(channelId);
	}

	public boolean containsSession(Session session) {
		return sessionIdMap.containsValue(session);
	}

	public Session findBySessionId(String sessionId) {
		return sessionIdMap.get(sessionId);
	}

	public Session findByTerminalPhone(String phone) {
		String channelId = this.phoneMap.get(phone);
		if (channelId == null){
			return null;
		}
		return findBySessionId(channelId);
	}

	public synchronized Session put(String key, Session value) {
		if (value.getTerminalPhone() != null && !"".equals(value.getTerminalPhone().trim())) {
			this.phoneMap.put(value.getTerminalPhone(), value.getId());
		}
		return sessionIdMap.put(key, value);
	}

	public void removeBySessionId(String sessionId) {
		if (sessionId == null){
			return ;
		}
		Session session = findBySessionId(sessionId);
		if (session == null){
			return ;
		}
		if (session.getTerminalPhone() != null){
			this.phoneMap.remove(session.getTerminalPhone());
		}
		sessionIdMap.remove(sessionId);
	}
}
