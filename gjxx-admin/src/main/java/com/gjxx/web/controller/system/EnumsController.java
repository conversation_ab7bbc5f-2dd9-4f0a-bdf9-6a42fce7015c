package com.gjxx.web.controller.system;

import com.gjxx.common.core.controller.BaseController;
import com.gjxx.common.core.domain.R;
import com.gjxx.common.factory.EnumFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
public class EnumsController extends BaseController {

    /**
     * 根据Key值获取枚举
     *
     * @return
     */
    @GetMapping("/system/enums")
    public R cacheEnums() {
        Map<String, Object> enums = EnumFactory.selectAll();
        return R.ok(enums);
    }

}
