package com.gjxx.buss.service.impl;

import com.gjxx.common.mp.service.impl.MpServiceImpl;
import java.util.List;
import org.springframework.stereotype.Service;
import com.gjxx.buss.mapper.TCarDevMapper;
import com.gjxx.buss.domain.TCarDev;
import com.gjxx.buss.service.ITCarDevService;

/**
 * 车辆设备信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class TCarDevServiceImpl extends MpServiceImpl<TCarDevMapper, TCarDev> implements ITCarDevService {

    /**
     * 查询车辆设备信息列表
     *
     * @param tCarDev 车辆设备信息
     * @return 车辆设备信息
     */
    @Override
    public List<TCarDev> selectTCarDevList(TCarDev tCarDev) {
        return baseMapper.selectTCarDevList(tCarDev);
    }

    @Override
    public List<TCarDev> listChildByCarTeamId(Long carTeamId) {
        return baseMapper.listChildByCarTeamId(carTeamId);
    }

    @Override
    public List<String> listChildSimNoByCarTeamId(Long carTeamId) {
        return baseMapper.listChildSimNoByCarTeamId(carTeamId);
    }
}
