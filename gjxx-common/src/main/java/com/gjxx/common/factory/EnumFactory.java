package com.gjxx.common.factory;

import com.gjxx.common.repository.EnumRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;


/**
 * EnumFactory 枚举工厂
 * <AUTHOR>
 */
public class EnumFactory {

    public static final Logger log = LoggerFactory.getLogger(EnumFactory.class);

    private final static EnumRepository repository = EnumRepository.getInstance();

    private static String[] paths = new String[]{"com.gjxx.**.enums"};

    //------------------------------------------read------------------------------

    /**
     * 查询所有
     *
     * @return
     */
    public static Map<String, Object> selectAll() {
        return repository.selectAll(paths);
    }

    //------------------------------------------派生方法------------------------------

    /**
     * 根据LoadName名称获取
     *
     * @param id
     * @return
     */
    @SuppressWarnings("unchecked")
    public static List<Map<String, Object>> getById(String id) {

        Map<String, Object> result = selectAll();

        if (result == null) {
            return null;
        }

        return (List<Map<String, Object>>) result.get(id);
    }

    //------------------------------------------remove------------------------------

    /**
     * 删除所有
     */
    public static void removeAll() {
        repository.removeAll();
    }

}
