<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gjxx.buss.mapper.TCarDevMapper">


    <select id="selectTCarDevList" parameterType="com.gjxx.buss.domain.TCarDev" resultType="com.gjxx.buss.domain.TCarDev">
        select * from t_car_dev
        <where>
            <if test="vehicleId != null  and vehicleId != ''">
                and vehicle_id like concat('%', #{vehicleId}, '%')
            </if>
            <if test="vehicleNoColor != null  and vehicleNoColor != ''">
                and vehicle_no_color = #{vehicleNoColor}
            </if>
            <if test="simNo != null  and simNo != ''">
                and sim_no like concat('%', #{simNo}, '%')
            </if>
            <if test="carTeamId != null ">
                and car_team_id = #{carTeamId}
            </if>
            <if test="devModelId != null ">
                and dev_model_id = #{devModelId}
            </if>
            <if test="vedioCh != null  and vedioCh != ''">
                and vedio_ch = #{vedioCh}
            </if>
            <if test="createUser != null  and createUser != ''">
                and create_user = #{createUser}
            </if>
            <if test="updateUser != null  and updateUser != ''">
                and update_user = #{updateUser}
            </if>
        </where>
    </select>
    <select id="listChildByCarTeamId" resultType="com.gjxx.buss.domain.TCarDev">
        SELECT * FROM t_car_dev WHERE FIND_IN_SET(car_team_id,car_team_child_list(#{carTeamId}))
    </select>

    <select id="listChildSimNoByCarTeamId" resultType="String">
        SELECT sim_no FROM t_car_dev WHERE FIND_IN_SET(car_team_id,car_team_child_list(#{carTeamId}))
    </select>
</mapper>
