package com.gjxx.common.enums;

import lombok.Data;

/**
 * 报警类型
 */
public enum WarningTypeEnum {
    one("01", "紧急报警"),
    two("02", "超速报警"),
    three("03", "疲劳驾驶"),
    four("04", "危险预警"),
    five("05", "GNSS模块发生故障"),
    six("06", "GNSS天线未接或被剪断"),
    seven("07", "GNSS天线短路"),
    eight("08", "终端主电源欠压"),
    nine("09", "终端主电源掉电"),
    ten("10", "终端LCD或显示器故障"),
    eleven("11", "TTS模块故障"),
    twelve("12", "摄像头故障"),
    thirteen("13", "道路运输证IC卡模块故障"),
    fourteen("14", "超速预警"),
    fifteen("15", "疲劳驾驶预警"),
    nineteen("19", "当天累计驾驶超时"),
    twenty("20", "超时停车"),
    twentyone("21", "进出区域"),
    twentytwo("22", "进出路线"),
    twentythree("23", "路段行驶时间不足/过长"),
    twentyfour("24", "路线偏离报警"),
    twentyfive("25", "车辆VSS故障"),
    twentysix("26","车辆油量异常"),
    twentyseven("27","车辆被盗"),
    twentyeight("28","车辆非法点火"),
    twentynine("29","车辆非法位移"),
    thirty("30","碰撞预警"),
    thirtyone("31","侧翻预警"),
    thirtytwo("32","非法开门报警");

    private final String code;
    private final String info;

    WarningTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
