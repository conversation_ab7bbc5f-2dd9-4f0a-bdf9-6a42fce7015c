import request from '@/utils/request'

// 查询车队信息列表
export function listCarTeam(query) {
  return request({
    url: '/buss/carTeam/list',
    method: 'get',
    params: query
  })
}

// 查询车队信息详细
export function getCarTeam(id) {
  return request({
    url: '/buss/carTeam/' + id,
    method: 'get'
  })
}

// 新增车队信息
export function addCarTeam(data) {
  return request({
    url: '/buss/carTeam',
    method: 'post',
    data: data
  })
}

// 修改车队信息
export function updateCarTeam(data) {
  return request({
    url: '/buss/carTeam',
    method: 'put',
    data: data
  })
}

// 删除车队信息
export function delCarTeam(id) {
  return request({
    url: '/buss/carTeam/' + id,
    method: 'delete'
  })
}
