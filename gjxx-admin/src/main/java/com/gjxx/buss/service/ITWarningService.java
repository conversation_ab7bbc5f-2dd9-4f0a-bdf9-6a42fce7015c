package com.gjxx.buss.service;

import com.gjxx.common.mp.service.MpService;
import java.util.List;
import java.util.Map;
import com.gjxx.buss.domain.TWarning;

/**
 * 报警信息Service接口
 *
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface ITWarningService extends MpService<TWarning> {

    /**
     * 查询报警信息列表
     *
     * @param tWarning 报警信息
     * @return 报警信息集合
     */
    List<TWarning> selectTWarningList(TWarning tWarning);

    /**
     * 批量处理报警信息
     */
    int handleBatchByIds(Long[] ids,String userName);

    /**
     * 批量处理报警信息
     */
    int handleNoByIds(Long[] ids,String userName);
}
