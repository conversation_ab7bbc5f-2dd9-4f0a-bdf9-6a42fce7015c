cd `dirname $0`
CURRENT_DIR=`pwd`
cd $CURRENT_DIR

# shellcheck disable=SC2164
cd ../gjxx-ui

svn update

echo ----------------------------------------------------
echo + + 代码更新
echo  ---------------------------------------------------

echo --------------开始安装组件-----------
npm install --registry=https://registry.npmmirror.com
echo --------------安装组件完成-----------

echo --------------开始构建测试环境-----------
# 构建测试环境
npm run build:test
echo --------------测试环境构建完成-----------

echo --------------`pwd`-----------
