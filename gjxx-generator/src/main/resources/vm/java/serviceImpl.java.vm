package ${packageName}.service.impl;

import com.gjxx.common.mp.service.impl.MpServiceImpl;
import java.util.List;
    #foreach ($column in $columns)
        #if($column.javaField == 'createTime' || $column.javaField == 'updateTime')
            #break
        #end
    #end
import org.springframework.stereotype.Service;
    #if($table.sub)

    import ${packageName}.domain.${subClassName};
    #end
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;

/**
 * ${functionName}Service业务层处理
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
public class ${ClassName}ServiceImpl extends MpServiceImpl<${ClassName}Mapper, ${ClassName}> implements I${ClassName}Service {

    /**
     * 查询${functionName}列表
     *
     * @param ${className} ${functionName}
     * @return ${functionName}
     */
    @Override
    public List<${ClassName}> select${ClassName}List(${ClassName} ${className}) {
        return baseMapper.select${ClassName}List(${className});
    }

}
