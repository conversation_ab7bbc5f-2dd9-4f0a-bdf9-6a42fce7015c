package com.gjxx.common.tcp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 参数查询
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ParamQueryMsg extends PackageData{
	// 应答流水号
	// byte[0-1]
	private int msgFlowId;
	// 应答参数个数
	// byte[2-3]
	private long paramNum;
	// byte[3-x] 参数项列表
	private List<Param> paramList;

	public ParamQueryMsg() {}

	public ParamQueryMsg(PackageData packageData) {
		this();
		this.channel = packageData.getChannel();
		this.checkSum = packageData.getCheckSum();
		this.calculatedCheckSum = packageData.getCalculatedCheckSum();
		this.msgBodyBytes = packageData.getMsgBodyBytes();
		this.msgHeader = packageData.getMsgHeader();
	}

	@Data
	public static class Param implements Serializable {
		// byte[0-2] 参数ID
		private int paramId;
		// byte[3-4] 参数长度
		private int paramLong;
		// byte[4-x] 参数值
		private String paramValue;
	}
}
