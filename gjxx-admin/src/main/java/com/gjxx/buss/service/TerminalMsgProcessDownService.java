package com.gjxx.buss.service;


import com.gjxx.buss.domain.bean.*;
import com.gjxx.common.core.domain.R;
import com.gjxx.common.tcp.HistoryVideoMsg;
import com.gjxx.common.tcp.ParamQueryMsg;
import com.gjxx.netty.req.VideoPlayParam;

/**
 * 调用终端下行服务
 * <AUTHOR>
 * @date 2018/06/04
 */
public interface TerminalMsgProcessDownService {
	/**
	 * 位置信息查询
	 */
    R<String> locationQuery(String phone);
	/**
	 * 向终端下发实时音视频传输请求,并且要求向指定视频服务器推送音视频数据
	 */
	R<String> realPlayRequest(VideoPlayParam videoPlayParam);
	/**
	 * 向终端下发实时音视频传输控制
	 */
	R<String> realPlayControl(VideoPlayParam videoPlayParam);
//	/**
//	 * 向终端下发实时音视频传输状态通知
//	 * <AUTHOR>
//	 * @return com.gjxx.core.utils.VideoReturn
//	 */
//	VideoReturn realPlayNotifyStatus(VideoParam videoParam);
	/**
	 * 向终端下发远程录像回放请求,并且要求向指定视频服务器推送音视频数据
	 */
	R<VideoRtmpParamReturn> playbackRequest(VideoParamBean videoParam);
	/**
	 * 向终端下发远程录像回放控制
	 */
	R<String> playbackControl(VideoPlayParam videoPlayParam);
//	/**
//	 * 查询终端音视频属性
//	 * <AUTHOR>
//	 * @return com.gjxx.core.utils.VideoReturn
//	 */
//	VideoReturn getAVideoAttributes(VideoParam videoParam);
//
	/**
	 * 获取终端历史视频资源列表
	 * @param selectCarDevChn 逻辑通道号
	 * @param mobileNumber 手机号
	 */
	R<HistoryVideoMsg> historyVideo(String selectCarDevChn, String mobileNumber, String startTime, String endTime);

	/**
	 * 发送设备拍照命令
	 * @param photoScreenDTO
	 * @return
	 */
    R<String> photoScreen(PhotoScreenDTO photoScreenDTO);

	/**
	 * 文本信息下发命令
	 * @param textSendDTO
	 * @return
	 */
	R<String> textSend(TextSendDTO textSendDTO);

	/**
	 * 设备参数查询命令
	 * @param paramSelectDTO
	 * @return
	 */
	R<ParamSelectVO> paramSelect(ParamSelectDTO paramSelectDTO);

	/**
	 * 设备参数设置命令
	 * @param paramSetDTO
	 * @return
	 */
	R<String> paramSet(ParamSetDTO paramSetDTO);

	/**
	 * 电话回拨命令
	 * @param phoneCarDTO
	 * @return
	 */
	R<String> phoneCar(PhoneCarDTO phoneCarDTO);
}
