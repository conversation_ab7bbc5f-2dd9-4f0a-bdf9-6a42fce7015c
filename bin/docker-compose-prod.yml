
#
# 列出所有运行容器
# docker-compose ps
#
# 查看服务日志输出
# 全部：docker-compose logs
# docker-compose logs gjxx-admin
#
# 构建或者重新构建服务
# 全部：docker-compose build
# 指定：docker-compose build gjxx-admin
#
# 启动指定服务已存在的容器
# 全部：docker-compose start
# 指定：docker-compose start gjxx-admin
#
# 停止已运行的服务的容器
# 全部：docker-compose stop
# 指定：docker-compose stop gjxx-admin
#
# 删除指定服务的容器
# 全部：docker-compose rm
# 指定：docker-compose rm gjxx-admin
#
# 通过发送 SIGKILL 信号来停止服务的容器
# 全部：docker-compose kill
# 指定：docker-compose kill gjxx-admin
#

version: '3'
services:
  # gjxxAdmin
  gjxx-admin:
    # 指定 Dockerfile 所在路径
    build: ../gjxx-admin
    container_name: gjxxAdmin
    # 指定端口映射
    ports:
      - "46000:8080"
      - "9505:7788"
    volumes:
      - /etc/localtime:/etc/localtime:ro # 设置容器时区与宿主机保持一致
      - /mnt/logs/gjxx-admin:/log/gjxx-admin
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
  # gjxx-video-server
  gjxx-video-server:
    # 指定 Dockerfile 所在路径
    build: ../gjxx-video-server
    container_name: gjxxVideoServer
    # 指定端口映射
    ports:
      - "7789:7789"
      - "1935:1935"
    volumes:
      - /etc/localtime:/etc/localtime:ro # 设置容器时区与宿主机保持一致
      - /mnt/logs/gjxx-video-server:/log/gjxx-video-server
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
