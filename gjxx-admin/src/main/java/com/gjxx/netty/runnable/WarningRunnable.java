package com.gjxx.netty.runnable;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gjxx.buss.domain.TWarning;
import com.gjxx.buss.service.ITWarningService;
import com.gjxx.common.enums.WarningTypeEnum;
import com.gjxx.common.tcp.LocationInfoUploadMsg;
import com.gjxx.common.utils.SpringBeanUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 */
@Slf4j
//@Component
public class WarningRunnable implements Runnable {
    /**
     * 消息文本
     */
    private String message;

    private ITWarningService itWarningService;

    public WarningRunnable(String message) {
        this.message = message;
        this.itWarningService = SpringBeanUtil.getBean(ITWarningService.class);
    }

    @Override
    public void run() {
        LocationInfoUploadMsg locationInfoUploadMsg = JSON.parseObject(message, LocationInfoUploadMsg.class);
        try {
            String warningFlagField = locationInfoUploadMsg.getWarningFlagField();
            String warningType = null;
            if(warningFlagField != null&& warningFlagField.length() == 32){
                if("1".equals(warningFlagField.substring(0,1))){
                    warningType = WarningTypeEnum.one.getCode();
                }else if("1".equals(warningFlagField.substring(1,2))){
                    warningType = WarningTypeEnum.two.getCode();
                }else if("1".equals(warningFlagField.substring(2,3))){
                    warningType = WarningTypeEnum.three.getCode();
                }else if("1".equals(warningFlagField.substring(3,4))){
                    warningType = WarningTypeEnum.four.getCode();
                }else if("1".equals(warningFlagField.substring(4,5))){
                     warningType = WarningTypeEnum.five.getCode();
                }else if("1".equals(warningFlagField.substring(5,6))){
                     warningType = WarningTypeEnum.six.getCode();
                }else if("1".equals(warningFlagField.substring(6,7))){
                     warningType = WarningTypeEnum.seven.getCode();
                }else if("1".equals(warningFlagField.substring(7,8))){
                     warningType = WarningTypeEnum.eight.getCode();
                }else if("1".equals(warningFlagField.substring(8,9))){
                     warningType = WarningTypeEnum.nine.getCode();
                }else if("1".equals(warningFlagField.substring(9,10))){
                     warningType = WarningTypeEnum.ten.getCode();
                }else if("1".equals(warningFlagField.substring(10,11))){
                     warningType = WarningTypeEnum.eleven.getCode();
                }else if("1".equals(warningFlagField.substring(11,12))){
                     warningType = WarningTypeEnum.twelve.getCode();
                }else if("1".equals(warningFlagField.substring(12,13))){
                     warningType = WarningTypeEnum.thirteen.getCode();
                }else if("1".equals(warningFlagField.substring(13,14))){
                     warningType = WarningTypeEnum.fourteen.getCode();
                }else if("1".equals(warningFlagField.substring(14,15))){
                     warningType = WarningTypeEnum.fifteen.getCode();
                }else if("1".equals(warningFlagField.substring(18,19))){
                     warningType = WarningTypeEnum.nineteen.getCode();
                }else if("1".equals(warningFlagField.substring(19,20))){
                     warningType = WarningTypeEnum.twenty.getCode();
                }else if("1".equals(warningFlagField.substring(20,21))){
                     warningType = WarningTypeEnum.twentyone.getCode();
                }else if("1".equals(warningFlagField.substring(21,22))){
                     warningType = WarningTypeEnum.twentytwo.getCode();
                }else if("1".equals(warningFlagField.substring(22,23))){
                     warningType = WarningTypeEnum.twentythree.getCode();
                }else if("1".equals(warningFlagField.substring(23,24))){
                     warningType = WarningTypeEnum.twentyfour.getCode();
                }else if("1".equals(warningFlagField.substring(24,25))){
                     warningType = WarningTypeEnum.twentyfive.getCode();
                }else if("1".equals(warningFlagField.substring(25,26))){
                     warningType = WarningTypeEnum.twentysix.getCode();
                }else if("1".equals(warningFlagField.substring(26,27))){
                     warningType = WarningTypeEnum.twentyseven.getCode();
                }else if("1".equals(warningFlagField.substring(27,28))){
                     warningType = WarningTypeEnum.twentyeight.getCode();
                }else if("1".equals(warningFlagField.substring(28,29))){
                     warningType = WarningTypeEnum.twentynine.getCode();
                }else if("1".equals(warningFlagField.substring(29,30))){
                     warningType = WarningTypeEnum.thirty.getCode();
                }else if("1".equals(warningFlagField.substring(30,31))){
                     warningType = WarningTypeEnum.thirtyone.getCode();
                }else if("1".equals(warningFlagField.substring(31,32))){
                     warningType = WarningTypeEnum.thirtytwo.getCode();
                }
            }
            String simNo = locationInfoUploadMsg.getMsgHeader().getTerminalPhone();
            if(warningType != null){
                List<String> handleStatusList = new ArrayList<>();
                handleStatusList.add("01");
                handleStatusList.add("03");
                List<TWarning> warningList = itWarningService.list(new QueryWrapper<TWarning>()
                        .eq("sim_no",simNo).eq("warning_type",warningType)
                        .in("handle_status", handleStatusList));
                log.info("warningType:{}",warningType);
                log.info("simNo:{}",simNo);
                log.info("报警信息:{}",warningList.size());
                if(warningList.size() > 0){
                    TWarning warning = warningList.get(0);
                    warning.setSimNo(simNo);
                    warning.setWarningCount(warning.getWarningCount()+1);
                    warning.setUpdateTime(new Date());
                    itWarningService.updateById(warning);
                }else {
                    TWarning tWarning = new TWarning();
                    tWarning.setSimNo(simNo);
                    tWarning.setWarningType(warningType);
                    tWarning.setWarningCount(1L);
                    tWarning.setHandleStatus("01");
                    itWarningService.save(tWarning);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("[报警信息消息队列消费] 保存报警信息失败:{}",e.getMessage());
        }

    }
}
