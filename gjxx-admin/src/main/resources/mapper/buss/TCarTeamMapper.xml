<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gjxx.buss.mapper.TCarTeamMapper">


    <select id="selectTCarTeamList" parameterType="com.gjxx.buss.domain.TCarTeam" resultType="com.gjxx.buss.domain.TCarTeam">
        select * from t_car_team
        <where>
            <if test="name != null  and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="nameAlias != null  and nameAlias != ''">
                and name_alias like concat('%', #{nameAlias}, '%')
            </if>
            <if test="parentId != null ">
                and parent_id = #{parentId}
            </if>
            <if test="orderNum != null ">
                and order_num = #{orderNum}
            </if>
            <if test="whetherCompany != null  and whetherCompany != ''">
                and whether_company = #{whetherCompany}
            </if>
            <if test="createUser != null  and createUser != ''">
                and create_user = #{createUser}
            </if>
            <if test="updateUser != null  and updateUser != ''">
                and update_user = #{updateUser}
            </if>
        </where>
        order by parent_id, order_num
    </select>

    <select id="listChildCarTeamByCarTeamId" resultType="Long">
        SELECT id FROM t_car_team WHERE FIND_IN_SET(id,car_team_child_list(#{carTeamId}))
    </select>
</mapper>
