import request from '@/utils/request'
import {parseStrEmpty} from "@/utils/ruoyi";

// 查询车辆设备信息列表
export function listCarDev(query) {
  return request({
    url: '/buss/carDev/list',
    method: 'get',
    params: query
  })
}

// 查询设备型号
export function getDevModelList() {
  return request({
    url: '/buss/devModel/getDevModelList',
    method: 'get'
  })
}

// 查询车队下拉树结构
export function carTeamTreeSelect() {
  return request({
    url: '/buss/carTeam/carTeamTree',
    method: 'get'
  })
}

// 查询车辆设备信息详细
export function getCarDev(id) {
  return request({
    url: '/buss/carDev/' + id,
    method: 'get'
  })
}

// 新增车辆设备信息
export function addCarDev(data) {
  return request({
    url: '/buss/carDev',
    method: 'post',
    data: data
  })
}

// 修改车辆设备信息
export function updateCarDev(data) {
  return request({
    url: '/buss/carDev',
    method: 'put',
    data: data
  })
}

// 删除车辆设备信息
export function delCarDev(id) {
  return request({
    url: '/buss/carDev/' + id,
    method: 'delete'
  })
}

// 查询车队map
export function getCarTeamMap() {
  return request({
    url: '/buss/carTeam/getCarTeamMap',
    method: 'get'
  })
}

// 查询设备型号map
export function getDevModelMap() {
  return request({
    url: '/buss/devModel/getDevModelMap',
    method: 'get'
  })
}
