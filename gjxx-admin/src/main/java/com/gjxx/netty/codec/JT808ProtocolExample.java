package com.gjxx.netty.codec;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JT808协议使用示例
 * 展示如何使用JT808ProtocolUtils处理2013和2019版本的协议
 * 
 * <AUTHOR>
 */
public class JT808ProtocolExample {
    private static final Logger log = LoggerFactory.getLogger(JT808ProtocolExample.class);
    
    private JT808ProtocolUtils protocolUtils;
    
    public JT808ProtocolExample() {
        this.protocolUtils = new JT808ProtocolUtils();
    }
    
    /**
     * 示例：生成2013版本的消息头
     */
    public void example2013() {
        try {
            String phone = "13800138000";
            int msgType = 0x0200; // 位置信息汇报
            byte[] msgBody = new byte[]{0x01, 0x02, 0x03}; // 示例消息体
            int flowId = 1;
            
            // 生成2013版本的消息体属性
            int msgBodyProps = protocolUtils.generateMsgBodyProps(
                msgBody.length, // 消息体长度
                0,              // 不加密
                false,          // 无子包
                0               // 保留位
            );
            
            // 生成2013版本的消息头
            byte[] msgHeader = protocolUtils.generateMsgHeader(
                phone, msgType, msgBody, msgBodyProps, flowId
            );
            
            log.info("2013版本消息头长度: {}", msgHeader.length);
            log.info("2013版本消息体属性: 0x{}", Integer.toHexString(msgBodyProps));
            
        } catch (Exception e) {
            log.error("生成2013版本消息头失败", e);
        }
    }
    
    /**
     * 示例：生成2019版本的消息头
     */
    public void example2019() {
        try {
            String phone = "13800138000";
            int msgType = 0x0200; // 位置信息汇报
            byte[] msgBody = new byte[]{0x01, 0x02, 0x03}; // 示例消息体
            int flowId = 1;
            
            // 生成2019版本的消息体属性
            int msgBodyProps = protocolUtils.generateMsgBodyProps(
                msgBody.length,                           // 消息体长度
                0,                                       // 不加密
                false,                                   // 无子包
                0,                                       // 版本标识(由内部设置)
                JT808ProtocolUtils.ProtocolVersion.V2019 // 指定2019版本
            );
            
            // 生成2019版本的消息头
            byte[] msgHeader = protocolUtils.generateMsgHeader(
                phone, msgType, msgBody, msgBodyProps, flowId,
                JT808ProtocolUtils.ProtocolVersion.V2019
            );
            
            log.info("2019版本消息头长度: {}", msgHeader.length);
            log.info("2019版本消息体属性: 0x{}", Integer.toHexString(msgBodyProps));
            
        } catch (Exception e) {
            log.error("生成2019版本消息头失败", e);
        }
    }
    
    /**
     * 示例：自动检测协议版本
     */
    public void exampleAutoDetect() {
        // 模拟接收到的数据
        byte[] receivedData = new byte[]{
            0x02, 0x00,           // 消息ID
            0x40, 0x03,           // 消息体属性(2019版本，版本位=01)
            0x01,                 // 协议版本号(2019版本)
            // ... 其他数据
        };
        
        // 自动检测协议版本
        JT808ProtocolUtils.ProtocolVersion version = 
            JT808ProtocolUtils.detectProtocolVersion(receivedData);
        
        log.info("检测到的协议版本: {}", version.getDescription());
        
        // 根据版本获取消息头长度
        boolean hasSubPackage = false; // 根据实际消息体属性解析
        int headerLength = JT808ProtocolUtils.getMsgHeaderLength(version, hasSubPackage);
        log.info("消息头长度: {}", headerLength);
        
        // 根据版本获取手机号字段信息
        int phoneStartIndex = JT808ProtocolUtils.getPhoneStartIndex(version);
        int phoneLength = JT808ProtocolUtils.getPhoneLength(version);
        log.info("手机号字段位置: {} - {}", phoneStartIndex, phoneStartIndex + phoneLength - 1);
    }
    
    /**
     * 示例：解析消息体属性中的版本信息
     */
    public void exampleParseVersion() {
        // 2013版本的消息体属性示例
        int msgBodyProps2013 = 0x0003; // 版本位=00
        JT808ProtocolUtils.ProtocolVersion version2013 = 
            JT808ProtocolUtils.parseProtocolVersion(msgBodyProps2013);
        log.info("消息体属性 0x{} 对应版本: {}", 
            Integer.toHexString(msgBodyProps2013), version2013.getDescription());
        
        // 2019版本的消息体属性示例
        int msgBodyProps2019 = 0x4003; // 版本位=01
        JT808ProtocolUtils.ProtocolVersion version2019 = 
            JT808ProtocolUtils.parseProtocolVersion(msgBodyProps2019);
        log.info("消息体属性 0x{} 对应版本: {}", 
            Integer.toHexString(msgBodyProps2019), version2019.getDescription());
    }
    
    /**
     * 主方法，运行所有示例
     */
    public static void main(String[] args) {
        JT808ProtocolExample example = new JT808ProtocolExample();
        
        log.info("=== JT808协议工具类使用示例 ===");
        
        log.info("\n1. 生成2013版本消息头:");
        example.example2013();
        
        log.info("\n2. 生成2019版本消息头:");
        example.example2019();
        
        log.info("\n3. 自动检测协议版本:");
        example.exampleAutoDetect();
        
        log.info("\n4. 解析版本信息:");
        example.exampleParseVersion();
    }
}