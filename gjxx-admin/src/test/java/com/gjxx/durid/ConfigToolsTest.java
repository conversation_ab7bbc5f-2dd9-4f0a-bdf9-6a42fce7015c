package com.gjxx.durid;

import com.alibaba.druid.filter.config.ConfigTools;
import org.junit.jupiter.api.Test;

public class ConfigToolsTest {

    @Test
    public void testPassword() throws Exception {
        String passw = "Gjxx!1q2w3e";
        String[] arr = ConfigTools.genKeyPair(512);

        String privateKey = arr[0];
        System.out.println("privateKey:" + privateKey);

        String publicKey = arr[1];
//        publicKey = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJtBEwWHn5JW0UjzYtVFeYwTX7aRI7cSmOkXQNMa261rZbRbhZEpn/3CPwhHSFzPaPczU+V+6LpfjHSBhnIS7TUCAwEAAQ==";
        System.out.println("publicKey:" + publicKey);

        String password = ConfigTools.encrypt(arr[0], passw);
//        password = "U0GJSce2nbQk2F3i939TaAAT53WWPHwWBaHk/2iMywermVBaW9g7gS6NzJK7YdFTCAh9uLAyDJrVCJ88G1sFJw==";
        System.out.println("password:" + password);

        String ss = ConfigTools.decrypt(publicKey,password);
        System.out.println(ss);
    }

}
