package com.gjxx.buss.domain.bean;

import lombok.Data;

import java.io.Serializable;

/**
 * 车辆设备定位和视频播放bean
 */
@Data
public class CarDevLocationAndVedioPlayBean implements Serializable {

    private static final long serialVersionUID = -6327863245437894287L;

    /** 车辆标识 */
    private String vehicleId;

    /** 车牌颜色 */
    private String vehicleNoColor;

    /** SIM卡号 */
    private String simNo;

    /** 视频通道 */
    private String vedioChnNum;

    /** 公司名称 */
    private String carTeamName;

    /** 车辆设备状态 */
    private String carDevStatus;

    /** ACC点火开关状态 01-开，02-关 */
    private String accStatus;

    /** 报警状态 01-报警，02-未报警 */
    private String warningStatus;

    /** 最后定位经度 */
    private String longitude;

    /** 最后定位纬度 */
    private String latitude;

    /** 最后定位时间 */
    private String posTime;

    /** 定位地址 */
    private String locationAddress;

    /** 播放相对URL */
    private String playRelativeUrl;
    /**
     * 视频服务器地址
     */
    private String sinkIP;
    /**
     * 视频播放端口
     */
    private String sinkPlayPort;
    /**
     * 视频播放IP地址
     */
    private String sinkPlayIP;
    /**
     * 视频播放第二个端口
     */
    private String sinkPlayTwoPort;
    /**
     * 视频播放第三个端口
     */
    private String sinkPlayThreePort;
    /**
     * 视频播放第四个端口
     */
    private String sinkPlayFourPort;
    /**
     * 视频播放第五个端口
     */
    private String sinkPlayFivePort;
}
