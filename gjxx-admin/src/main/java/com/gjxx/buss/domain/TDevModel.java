package com.gjxx.buss.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.gjxx.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gjxx.common.core.domain.MpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 设备型号信息对象 t_dev_model
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName("t_dev_model")
@ApiModel(value = "设备型号信息")
public class TDevModel extends MpBaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty("ID")
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 设备型号 */
    @Excel(name = "设备型号")
    @ApiModelProperty("设备型号")
    private String devModel;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    @ApiModelProperty("供应商名称")
    private String supplierName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty("联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty("联系电话")
    private String contactPhone;

    /** 联系地址 */
    @Excel(name = "联系地址")
    @ApiModelProperty("联系地址")
    private String contactAddress;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty("状态")
    private String status;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private String createUser;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /** 修改人 */
    @Excel(name = "修改人")
    @ApiModelProperty("修改人")
    private String updateUser;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
