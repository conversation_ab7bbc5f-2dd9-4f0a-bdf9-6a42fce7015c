<template>

  <div class="login">
    <div class="header">
      <div style="padding-top: 3%;">
        <img class="logo-img" src="../assets/logo/logo.png">
        <span class="platName" id="platName">{{ setTitle() }}</span>
      </div>
    </div>
    <div class="login-content">
      <img class="login-bg" src="../assets/images/bgTaxi2.jpg">
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
<!--        <h3 class="title">用户登录</h3>-->
        <div class="login-title">
          <img style="height:37px;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAAAsCAMAAACaPkBsAAAAM1BMVEUA
                    AAAvx8kvx8kvx8kvx8kvx8kvx8kvx8kvx8kvx8kvx8kvx8kvx8kvx8kvx8kvx8kvx8mJBTvjAAAAEHRSTlMAu0R3
                    mSEz7xHdqohmVcy/Bv+90gAAAnZJREFUWMPtl+2ynCAMQAPhW7G8/9N2L0GICwzebm9nOuP55QKGYwjiwl2czLib
                    A/umHp07NzGHxzjSFwp6ZGp4Ay5fRDASlTBsRM8GL0SaAw2a/3ALgXQAhPTC0k85EyDMbQHF58eMHgpYgJgKawG8
                    K7BRcEpomSFAQSPhzzWy6USYWQ1g7t+BoRXdAz17TRj3iUBoqZuWPLv9tpsWuxOgyTRcEBOBmKouH2ldWwKh0Na7
                    TRbQX0WIkME05KgrKumqhnB7SEmVCYwt69VwuSl0NWB1rVhiJsB7Pc3KBKLi/c4ONmAcl9iBGXFTwNR0+jbsUDRh
                    1Qr0bHiBGuk2vR1pjA0Sxmy5m10PPbciOCfC7Al50ezY4bM/LT6WZOrcSnjRwoc0hTJsNpuGeAfLR8CyhCGyeYKE
                    0DaJtGnKzuOrrka2WwI6XbCoy7vEA6FRTNh5/E1yDl5lixwmBt/JAZas4tNGN1Ur0liUFd0W2aOqAkhyhajEABVP
                    AR9m86Nh57E+N32S58ux9L60/Gb4iygn3dQt08MXXYNKY6yDwvXwlecFcLgARy9q8AyGkkNJkVxAjAVEw2drXmT1
                    JJqngEfl3BEQ1DUHW17wiqCC5QIKL1wFRCeQ7gjsXUFW8CrQ0wvYTkAtBATMBeT3BVInkKO+181Rfxv4ewL4LiAp
                    6vKDZC1AGL4rHH2cdUEcF6h+DPxzgfIqULuUshxqutmVDSbfBRSiHgfl4LpI5kiKS0hsrfXyhwUME7COzRr/jYCF
                    JuANIDui1EDAiBfbtwV2MSPwvwV1pHLsjHTw8PDw8LBAi/QJQsOHiPQZv+BDbPqQ/z4DP14DvwGNZ4WRtYinPwAA
                    AABJRU5ErkJggg=="/>
        </div>
        <div class="reg-box">
          <div class="user">
            <div class="form-img">
              <img style="width: 26px;height: 26px;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAA
                        WCAYAAAAvg9c4AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF
                        3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAAsSAAALEgHS3X78AAACJ0lEQVQ4y63UP2iVZxQG8F9
                        uUksqaqomSIeGwpVWcehQqIs6VDhiY9ApUJxcHbpooJoMeqFLSqEBNx10EFMcFOqfAy6tOFsU3QJaqhasEv/gI
                        CoO9w1+Xu+9TaRnOd93zvs873PO+563RxeLiPWYwDZ8jLs4i0Zm3uuE6+lCOIJf0Y+n+AefYgnuIzLzajtsrQP
                        hME7hQ4xjdWauxRCmMYgzEdHfDt/XQeh+LMXBzJyaD2bmI3wfEavxHfbgyIKU4hu8KKra2VRl3cLKL+Xdz8ynH
                        fK3il+1GNJ7GIqIlR3y6yrrFkx6Dr34oTURET04UH4vLIb0ZzzEvoj4KSKGCuEwZjCCmzjZDtztngYuVkJPsKx
                        8P8dXmXm9Hba3DVlfvV6f0Dz5JZXUK3xQwe2q1+tzs7Oz7wxArYVwEH/gUMkdwRb0Z+ZHRem3moMxiKMRcToiq
                        pu/KT8iluMKNuAydmfmX13a82Uh/xznMZqZL1uVHi+Ev2FrlXBgprFxYKZxaWCmsWY+lpl/4mtcxXY03io/InZ
                        hJ25gLDOftwjbpjk966rBMrY78ADjEbEBauXe/VjW7c3MZxZhmXkHB8vhHZ5Xuhlf4FJm/r4Ywoodw22MRsQnN
                        YyWxIkuoL+Lf9BB7YtyaL3YXtO8MnQYOZgbmzyKFXNjk9e6bHy++E01fKb5Iv3brb65scnH/9GCG8XX+zCAlxF
                        x6z372Wor+/AL9mL4fyB8hunXjnGfKelCwxsAAAAASUVORK5CYII="/></div>
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                type="text"
                auto-complete="off"
                placeholder="账号"
                style="width: 89%"
              >
<!--                <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon"/>-->
              </el-input>
            </el-form-item>
          </div>

          <div class="user">
            <div class="form-img">
              <img style="width: 26px;height: 26px;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAXC
                        AYAAADtNKTnAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3Cc
                        ulE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAAsSAAALEgHS3X78AAABqUlEQVQ4y53UT4iNYRTH8c8d74w
                        SGpSysGCshCbZ2Cqd2dhIXSU2FlYsrKTuQsPGwiRWNjSruQplQcfWxkJI+ZcMmZUSN5GFy1jcd7jdmXtn7vzq6f
                        3T73zfc877nKdiAUXEDpzFGNbhE+7hfGZOd/orCwAOYxKD+IIZbMUa/MDBzHzQHjPQARgtAU0cx8bMHMUGnMYQb
                        kXElva4oiORc2UGxzJzau5lZv7CRETMYgJncGJeJhFRlD142w7o0FV8xYFu5awt033aBSAzm3iJTeVH50Hm7n/q
                        rWZ5HVywsctVJSJW4QL2YZf/v7WbRrAajzGFSwWu4UibaX25FtOecilQxXuMZua3pZYQESN4g+pACfrYDwAy8x2
                        +Y6joZRyuj48h8KhRrdW7+QZ6APbjvtbGmhqujx/tG6K1e180qrVteFhm1DfkObYP18fvYi+edDP26skkduMUbu
                        By35k0qrVZXC8frzSqtd/LKQfmAv/0Mi0GeY2TeNXLVImIJj5gZ2YuNsH/FBGbMY1nBe7gEBoRsWSI1hCuwO1C6
                        yz9rDXFK/uAzOAmLv4FKVB1z2+IFPcAAAAASUVORK5CYII="/></div>
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                auto-complete="off"
                placeholder="密码"
                @keyup.enter.native="handleLogin"
                style="width: 89%"
              >
                <!--              <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>-->
              </el-input>
            </el-form-item>
          </div>

          <div class="user">
            <div class="form-img">
              <img style="width: 26px;height: 26px;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUg
                        AAABMAAAAWCAYAAAAinad/AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAA
                        A6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAAsSAAALEgHS3X78AAACQklEQVQ4y6XUz6vUVR
                        jH8dcMSqF1nfJHYASKYEKJuNONqGAPFLkwbcAfIboILm5ay4A5C/8AXbgRfwuTGupF5CExIQWFIBEKEoVaiL/IO7X
                        zmt4W98xlGGbkSp/Nw/d8nvM+5zzne56KAYqI97EFm7EUP+EETmfm3/3mVHoAQ/gCW7Gm+OP4AwtL2jOMFPClzByb
                        hEVEFZ9iG9bjzeLdxkmczMz7EfFRWWQLPig5T/EdjmbmjUpEXMDnxbyPUziRmbcHHL+KVQX6JYaK1axExDgeFvNqZ
                        r40RUXEGwV4DLemlfFHmXllqpCOMvNZRIx0SlZ9XcCr9NqwWqs5vdZqzv3fsFqrORvX8LDWas7v9Ts1e7t3dSzHz+1
                        642UZew+X8XGJj/vB/sGcnvGvsR+na63mVszDVSzCBWxq1xv/ltzO3NEq7mAoIhZ0wc7iN2xC4mYBncKGdr0x1pW7t
                        MQ7VVwvH+s6brveeIDVuFXifBzEtna98aLnFJ+UeL2K78vHju6Mdr3xBGvxJy5huFO/jiJiBup4jovTTHSDX7EiItZ
                        l5g9dwFEsMFi78I6J5/dXNTPHsbuYByJipikoIhahUXb1LeU/y8zzOIfFOFIe86tAs8olvYW9mXl3Ela0E79jIw5Hx
                        PQBoHdLDZeVuK/jTcIy8ykC9/AVrkXEhz2gNfgFK/EjNmbm5O1W+qw8F2dM9KwxHC872I7PypxDGO7usn1hBVjFMPZ
                        gdpd1D99k5ki/eX1hXdAZ2IAluGGi578YlP8f4Di7HRx6bcwAAAAASUVORK5CYII="/></div>
            <el-form-item prop="code" v-if="captchaEnabled">
              <el-input
                v-model="loginForm.code"
                auto-complete="off"
                placeholder="验证码"
                style="width: 54%"
                @keyup.enter.native="handleLogin"
              >
<!--                <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon"/>-->
              </el-input>
              <div class="login-code">
                <img :src="codeUrl" @click="getCode" class="login-code-img"/>
              </div>
            </el-form-item>
          </div>


<!--          <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;display: none">记住密码</el-checkbox>-->
        </div>
        <div class="sub">
          <el-form-item style="width:100%;">
            <el-button
              :loading="loading"
              size="medium"
              type="primary"
              style="width:100%;"
              @click.native.prevent="handleLogin"
            >
              <span v-if="!loading">登 录</span>
              <span v-else>登 录 中...</span>
            </el-button>
<!--            <div style="float: right;" v-if="register">-->
<!--              <router-link class="link-type" :to="'/register'">立即注册</router-link>-->
<!--            </div>-->
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!--  底部  -->
<!--    <div class="el-login-footer">-->
<!--      <span>版权所有：XXXX股份有限公司 技术支持：XXXX股份有限公司</span>-->
<!--    </div>-->
    <footer>
      <p>
        <a href="javascript:void (0)">版权所有：XXXX股份有限公司</a>
        <a href="javascript:void (0)">技术支持：XXXX股份有限公司</a>
      </p>
    </footer>
  </div>
</template>

<script>
  import {getCodeImg} from "@/api/login";
  import Cookies from "js-cookie";
  import {encrypt, decrypt} from '@/utils/jsencrypt'
  import md5 from 'js-md5'

  export default {
    name: "Login",
    data() {
      return {
        codeUrl: "",
        loginForm: {
          // username: "",
          // password: "",
          rememberMe: false,
          code: "",
          uuid: ""
        },
        loginRules: {
          username: [
            {required: true, trigger: "blur", message: "请输入您的账号"}
          ],
          password: [
            {required: true, trigger: "blur", message: "请输入您的密码"}
          ],
          code: [{required: true, trigger: "change", message: "请输入验证码"}]
        },
        loading: false,
        // 验证码开关
        captchaEnabled: true,
        // 注册开关
        register: false,
        redirect: undefined
      };
    },
    watch: {
      $route: {
        handler: function (route) {
          this.redirect = route.query && route.query.redirect;
        },
        immediate: true
      }
    },
    created() {
      this.getCode();
      this.getCookie();
    },
    methods: {
      getCode() {
        getCodeImg().then(res => {
          this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
          if (this.captchaEnabled) {
            this.codeUrl = "data:image/gif;base64," + res.img;
            this.loginForm.uuid = res.uuid;
          }
        });
      },
      getCookie() {
        const username = Cookies.get("username");
        const password = Cookies.get("password");
        const rememberMe = Cookies.get('rememberMe')
        this.loginForm = {
          username: username === undefined ? this.loginForm.username : username,
          password: password === undefined ? this.loginForm.password : decrypt(password),
          rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
        };
      },
      handleLogin() {
        this.$refs.loginForm.validate(valid => {
          if (valid) {
            this.loading = true;
            let loginForm = this.loginForm;
            let password = loginForm.password;
            if (this.loginForm.rememberMe) {
              Cookies.set("username", this.loginForm.username, {expires: 30});
              Cookies.set("password", encrypt(this.loginForm.password), {expires: 30});
              Cookies.set('rememberMe', this.loginForm.rememberMe, {expires: 30});
            } else {
              Cookies.remove("username");
              Cookies.remove("password");
              Cookies.remove('rememberMe');
            }
            let data = {...loginForm};
            data.password = md5(password);
            this.$store.dispatch("Login", data).then(() => {
              this.$router.push({path: this.redirect || "/"}).catch(() => {
              });
            }).catch(() => {
              this.loading = false;
              if (this.captchaEnabled) {
                this.getCode();
              }
            });
          }
        });
      },
      setTitle() {
        let title = title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE;
        return title;
      }
    }
  };
</script>

<style rel="stylesheet/scss" lang="scss">

  ::-webkit-scrollbar {
    width: 5px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background-color: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #c0c0c0;
    border-radius: 3px;
  }

  .amap-container .amap-logo {
    display: none !important; //去掉高德地图logo
  }
  .amap-container .amap-copyright {
    display: none !important; //去掉高德地图logo
  }

  .amap-marker-label {
    padding: 5px 10px;
    background-color: #0e9aef;
    border-radius: 15px;
  }
  ::v-deep .amap-marker-label {
    padding: 5px 10px;
    background-color: #0e9aef;
    border-radius: 15px;
  }

  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{
    background-color: #b1ceef!important;
  }

  .el-button--primary{
    background-color:#2ec9be;
    border-color:#2ec9be;
  }
  .user div div div input{
    border-radius:0 4px 4px 0 !important;
  }

  .login {
    //display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    position: relative;
    width: 100%;
    height: 100%;
    background-image:url("../assets/images/backImage.png");
  }
  .header {
    padding-left: 8%;
    height: 20%;
    position: relative;
  }
  .form-img{
    float: left;
    width: 33px;
    height: 38px;
    padding: 4px 0 4px 6px;
    border: 1px solid #ddd;
    ine-height: 10px;
    border-right-style: none;
    line-height: 10px;
    border-radius:3px 0px 0px 3px;
  }
  .form-img img{
    padding:2px 6px 2px 0;
    //border-right: 1px solid #ddd;
  }

  .logo-img {
    width: 5%;
  }

  .login-title{
    padding: 0 0 0 34%;
    font-weight: bold;
  }
  .reg-box {
    padding:0 15px;
    margin-bottom: 0;
    margin-top: 22px;
  }
  .sub{
    text-align: center;
    padding: 0 15px;
  }

  .header span {
    font-size: 34px;
    font-weight: bold;
    color: #42627c;
    //position: relative;
    //margin-top:1%;
    margin-left: 1%;
    position: absolute;
    margin-top: 1%;
  }

  .login-content{
    position: relative;
    width: 100%;
    height: 65%;
    //margin-top:2%;
  }

  .login-form {
    border-radius: 10px;
    background: #ffffff;
    width: 400px;
    padding: 25px 25px 5px 25px;

    position: absolute;
    top: 50%;
    margin-top: -173px;
    right: 10%;

    .el-input {
      height: 38px;

      input {
        height: 38px;
      }
    }

    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 2px;
    }
  }

  .el-login-footer {
    height: 15%;
    //line-height: 40px;
    position: relative;
    bottom: 0;
    width: 100%;
    text-align: center;
    //color: #fff;
    font-family: Arial;
    font-size: 14px;
    letter-spacing: 1px;
  }

  .el-login-footer span{
    color: #333;
    position: absolute;
    top: 1%;
    margin-left: -14%;
    margin-top: 2.5%;
  }

  footer {
    height: 15%;
  }

  footer p {
    text-align: center;
    color: #333;
    font-size: 14px;
    position: relative;
    top: 50%;
    margin-top: -5px;
  }

  footer p a {
    color: #333;
    text-decoration: none;
    margin-right: 1%;
  }


  .login-bg {
    width: 100%;
    height: 100%;
  }

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #707070;
  }



  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }

  .login-code {
    width: 33%;
    height: 38px;
    float: right;

    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }



  .login-code-img {
    height: 38px;
    width: 100%;
    border-radius: 4px;
    margin-bottom: 1px;
  }
</style>
