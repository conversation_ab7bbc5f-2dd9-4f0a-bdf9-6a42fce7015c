package com.gjxx.buss.service;

import com.gjxx.common.mp.service.MpService;
import java.util.List;
import java.util.Map;
import com.gjxx.buss.domain.TDevModel;

/**
 * 设备型号信息Service接口
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface ITDevModelService extends MpService<TDevModel> {

    /**
     * 查询设备型号信息列表
     *
     * @param tDevModel 设备型号信息
     * @return 设备型号信息集合
     */
    List<TDevModel> selectTDevModelList(TDevModel tDevModel);
    /**
     * 查询所有设备型号
     * @return 设备型号列表
     */
    List<TDevModel> selectDevModelAll();
}
