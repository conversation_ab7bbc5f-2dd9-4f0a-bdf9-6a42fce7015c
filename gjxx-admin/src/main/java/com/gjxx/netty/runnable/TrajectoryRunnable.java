package com.gjxx.netty.runnable;

import com.alibaba.fastjson2.JSON;
import com.gjxx.buss.domain.TMediaFile;
import com.gjxx.buss.domain.bean.TraceVo;
import com.gjxx.buss.service.ITMediaFileService;
import com.gjxx.common.tcp.LocationInfoUploadMsg;
import com.gjxx.common.tcp.MediaFileMsg;
import com.gjxx.common.utils.SpringBeanUtil;
import com.gjxx.mongo.entity.TrajectoryPoint;
import com.gjxx.mongo.service.TrajectoryPointService;
import com.gjxx.system.service.ISysFileService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;

import java.io.IOException;


/**
 */
@Slf4j
public class TrajectoryRunnable implements Runnable {
    /**
     * 消息文本
     */
    private String message;

    private TrajectoryPointService trajectoryPointService;

    public TrajectoryRunnable(String message) {
        this.message = message;
        this.trajectoryPointService = SpringBeanUtil.getBean(TrajectoryPointService.class);
    }

    @Override
    public void run() {
        LocationInfoUploadMsg locationInfoUploadMsg = JSON.parseObject(message, LocationInfoUploadMsg.class);
        try {
            TrajectoryPoint trajectoryPoint = new TrajectoryPoint();
            trajectoryPoint.setId(locationInfoUploadMsg.getMsgHeader().getTerminalPhone()+"_"+locationInfoUploadMsg.getTrajectoryTime());
            TraceVo traceVo = new TraceVo();
            traceVo.setSimNo(locationInfoUploadMsg.getMsgHeader().getTerminalPhone());
            traceVo.setLatitude(locationInfoUploadMsg.getLatitude());
            traceVo.setLongitude(locationInfoUploadMsg.getLongitude());
            traceVo.setPositionTime(locationInfoUploadMsg.getTrajectoryTime());
            trajectoryPoint.setTraceVoJson(JSON.toJSONString(traceVo));
            trajectoryPointService.save(trajectoryPoint);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("[轨迹定位消息队列消费] 保存轨迹定位失败:{}",e.getMessage());
        }

    }
}
