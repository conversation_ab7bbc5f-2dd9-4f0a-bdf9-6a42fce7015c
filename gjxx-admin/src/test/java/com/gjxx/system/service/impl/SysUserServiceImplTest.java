package com.gjxx.system.service.impl;

import com.gjxx.BaseServiceTest;
import com.gjxx.common.core.domain.entity.SysUser;
import com.gjxx.system.service.ISysUserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

//import static org.junit.jupiter.api.Assertions.*;

class SysUserServiceImplTest extends BaseServiceTest {

    @Autowired
    private ISysUserService sysUserService;

    @Test
    void getById() {
        SysUser byId = this.sysUserService.getById(1L);
        System.out.println("byId = " + byId);
    }

    @Test
    void selectUserById() {
        SysUser byId = this.sysUserService.selectUserById(1L);
        System.out.println("byId = " + byId);
    }
}
