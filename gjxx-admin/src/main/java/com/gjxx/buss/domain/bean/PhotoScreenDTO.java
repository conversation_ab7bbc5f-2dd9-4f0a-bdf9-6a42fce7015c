package com.gjxx.buss.domain.bean;

import lombok.Data;

import java.io.Serializable;

@Data
public class PhotoScreenDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 选择的车辆设备ID
     */
    private String selectCarDevId;
    /**
     * 选择的车辆设备sim卡号
     */
    private String simNo;
    /**
     * 对比度 0-127
     */
    private String contrast;
    /**
     * 亮度 0-255
     */
    private String bright;
    /**
     * 饱和度 0-127
     */
    private String saturation;
    /**
     * 色度 0-255
     */
    private String chroma;
    /**
     * 通道
     */
    private String chnId;
    /**
     * 分辨率
     */
    private String resolution;
    /**
     * 图像质量 1-10，1 代表质量损失最小，10 表示压缩比最大
     */
    private String imgQuality;
}
