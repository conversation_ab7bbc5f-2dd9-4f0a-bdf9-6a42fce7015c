package com.gjxx.buss.service;

import com.gjxx.common.mp.service.MpService;
import java.util.List;
import java.util.Map;
import com.gjxx.buss.domain.TCarDev;

/**
 * 车辆设备信息Service接口
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface ITCarDevService extends MpService<TCarDev> {

    /**
     * 查询车辆设备信息列表
     *
     * @param tCarDev 车辆设备信息
     * @return 车辆设备信息集合
     */
    List<TCarDev> selectTCarDevList(TCarDev tCarDev);

    /**
     * 根据车队ID递归查询车辆设备信息
     * @param carTeamId
     * @return
     */
    List<TCarDev> listChildByCarTeamId(Long carTeamId);
    /**
     * 根据车队ID递归查询车辆设备Sim卡号
     * @param carTeamId
     * @return
     */
    List<String> listChildSimNoByCarTeamId(Long carTeamId);
}
