package com.gjxx.buss.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gjxx.buss.domain.TCarDev;
import com.gjxx.buss.service.ITCarDevService;
import com.gjxx.common.core.domain.AjaxResult;
import com.gjxx.common.core.domain.R;
import com.gjxx.common.core.domain.entity.SysRole;
import com.gjxx.common.core.domain.entity.SysUser;
import com.gjxx.common.utils.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.gjxx.common.annotation.Log;
import com.gjxx.common.core.controller.BaseController;
import com.gjxx.common.enums.BusinessType;
import com.gjxx.buss.domain.TDevModel;
import com.gjxx.buss.service.ITDevModelService;
import com.gjxx.common.utils.poi.ExcelUtil;
import com.gjxx.common.core.page.TableDataInfo;

/**
 * 设备型号信息Controller
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/buss/devModel")
public class TDevModelController extends BaseController {
    @Autowired
    private ITDevModelService tDevModelService;
    @Autowired
    private ITCarDevService tCarDevService;
    /**
     * 查询设备型号信息列表
     */
    @PreAuthorize("@ss.hasPermi('buss:devModel:list')")
    @GetMapping("/list")
    public TableDataInfo list(TDevModel tDevModel) {
        startPage();
        List<TDevModel> list = tDevModelService.selectTDevModelList(tDevModel);
        return getDataTable(list);
    }

    /**
     * 导出设备型号信息列表
     */
    @PreAuthorize("@ss.hasPermi('buss:devModel:export')")
    @Log(title = "设备型号信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TDevModel tDevModel) {
        ExcelUtil<TDevModel> util = new ExcelUtil<>(TDevModel.class);

        List<TDevModel> list = tDevModelService.selectTDevModelList(tDevModel);
        util.exportExcel(response, list, "设备型号信息数据");
    }

    /**
     * 获取设备型号信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('buss:devModel:query')")
    @GetMapping(value = "/{id}")
    public R<TDevModel> getInfo(@PathVariable("id") Long id) {
        return R.ok(tDevModelService.getById(id));
    }

    /**
     * 新增设备型号信息
     */
    @PreAuthorize("@ss.hasPermi('buss:devModel:add')")
    @Log(title = "设备型号信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody TDevModel tDevModel) {
        long devModelCount = tDevModelService.count(new QueryWrapper<TDevModel>().eq("dev_model",tDevModel.getDevModel()));
        if(devModelCount > 0){
            return R.fail("设备型号已存在！");
        }
        tDevModel.setCreateUser(getUsername());
        tDevModel.setUpdateUser(getUsername());
        return R.ok(tDevModelService.save(tDevModel));
    }

    /**
     * 修改设备型号信息
     */
    @PreAuthorize("@ss.hasPermi('buss:devModel:edit')")
    @Log(title = "设备型号信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody TDevModel tDevModel) {
        List<TDevModel> tDevModelList = tDevModelService.list(new QueryWrapper<TDevModel>().eq("dev_model", tDevModel.getDevModel()));
        if(tDevModelList.size() > 0){
            if(!tDevModelList.get(0).getId().equals(tDevModel.getId())){
                return R.fail("设备型号已存在！");
            }
        }
        tDevModel.setUpdateUser(getUsername());
        return R.ok(tDevModelService.updateById(tDevModel));
    }

    /**
     * 删除设备型号信息
     */
    @PreAuthorize("@ss.hasPermi('buss:devModel:remove')")
    @Log(title = "设备型号信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        for(Long id : ids){
            long carDevCount = tCarDevService.count(new QueryWrapper<TCarDev>().eq("dev_model_id",id));
            if(carDevCount > 0){
                return R.fail("设备型号关联的有终端设备，不能删除！");
            }
        }
        return R.ok(tDevModelService.removeBatchByIds(CollectionUtils.arrayToList(ids)));
    }

    /**
     * 根据用户编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('buss:devModel:getDevModelList')")
    @GetMapping(value ="/getDevModelList")
    public AjaxResult getDevModelList() {
        AjaxResult ajax = AjaxResult.success();
        List<TDevModel> devModels = tDevModelService.selectDevModelAll();
        ajax.put("devModels", devModels);
        return ajax;
    }
    /**
     * 设备型号状态修改
     */
    @PreAuthorize("@ss.hasPermi('buss:devModel:changeStatus')")
    @Log(title = "设备型号状态修改", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody TDevModel tDevModel) {
        tDevModel.setUpdateUser(getUsername());
        tDevModel.setUpdateTime(new Date());
        return toAjax(tDevModelService.updateById(tDevModel));
    }

    /**
     * 查询设备型号map
     */
    @PreAuthorize("@ss.hasPermi('buss:devModel:getDevModelMap')")
    @GetMapping(value ="/getDevModelMap")
    public AjaxResult getDevModelMap() {
        AjaxResult ajax = AjaxResult.success();
        Map<Long, String> devModelMap = new HashMap<>();
        List<TDevModel> devModels = tDevModelService.selectDevModelAll();
        for(TDevModel devModel : devModels){
            devModelMap.put(devModel.getId(),devModel.getDevModel());
        }
        ajax.put("devModelMap", devModelMap);
        return ajax;
    }
}
