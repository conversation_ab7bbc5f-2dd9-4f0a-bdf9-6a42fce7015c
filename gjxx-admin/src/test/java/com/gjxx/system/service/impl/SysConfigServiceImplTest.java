package com.gjxx.system.service.impl;

import com.gjxx.BaseServiceTest;
import com.gjxx.system.domain.SysConfig;
import com.gjxx.system.service.ISysConfigService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SysConfigServiceImplTest extends BaseServiceTest {

    @Autowired
    private ISysConfigService service;

    @Test
    void getById() {
        SysConfig byId = this.service.getById(1L);
        System.out.println("byId = " + byId);
    }

    @Test
    void selectConfigById() {
        SysConfig sysConfig = service.selectConfigById(1L);
        System.out.println("sysConfig = " + sysConfig);
    }

}
