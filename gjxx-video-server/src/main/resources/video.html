<html>
<head>
    <title>HTTP-Flv Test</title>
</head>
<script type="text/javascript" src="//cdn.bootcss.com/flv.js/1.5.0/flv.min.js"></script>
<body>
<video id="xxoo" autoplay></video>
<script type="text/javascript">
    function play()
    {
        var el = document.getElementById('xxoo');

        var player = flvjs.createPlayer({
            type : 'flv',
            // url : '/video/' + location.hash.substring(1),
            url : 'http://***********:9502/video/014404980276-1',
            isLive : true,
            enableStashBuffer : false,
            enableWorker : true,
            stashInitialSize : 128
        });
        player.on('media_info', function()
        {
            console.log('media_info');
            console.log(player.mediaInfo);
            player.play();
        });
        player.attachMediaElement(el);
        player.load();
    }
</script>
<button onclick="play()">Play</button>
</body>
</html>
