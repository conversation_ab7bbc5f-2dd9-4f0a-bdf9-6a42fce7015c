package com.gjxx.common.exception;

/**
 * 消息透传异常
 */
public class MsgPassException extends RuntimeException {

    private static final long serialVersionUID = 8236089999363356118L;

    protected final String message;

    public MsgPassException(String message) {
        super(message);
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

}
