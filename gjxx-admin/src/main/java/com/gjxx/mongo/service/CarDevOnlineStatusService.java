package com.gjxx.mongo.service;

import com.gjxx.mongo.entity.CarDevOnlineStatus;

import java.util.List;

public interface CarDevOnlineStatusService {
    //定义一个查询方法
    CarDevOnlineStatus findCarDevOnlineStatusById(String id);
    //定义一个保存方法
    void save(CarDevOnlineStatus carDevOnlineStatus);
    //定义一个删除方法
    void delete(String id);
    //定义一个查询所有的方法
    List<CarDevOnlineStatus> findAll();
    //定义一个根据id集合查询方法
    List<CarDevOnlineStatus> findAllByIds(List<String> ids);
    //定义一个根据车队id集合查询方法
    List<CarDevOnlineStatus> findAllByCarTeamIds(List<Long> tCarTeamIds);
}

