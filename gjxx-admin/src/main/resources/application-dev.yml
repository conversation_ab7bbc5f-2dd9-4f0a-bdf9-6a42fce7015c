# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
# 日志配置
logging:
  level:
    com.gjxx: debug
    org.springframework: warn

publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJtBEwWHn5JW0UjzYtVFeYwTX7aRI7cSmOkXQNMa261rZbRbhZEpn/3CPwhHSFzPaPczU+V+6LpfjHSBhnIS7TUCAwEAAQ==
# Spring配置
spring:
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # rabbitmq 配置
  rabbitmq:
    host: localhost
    port: 5672
    username: admin
    password: 123456
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 7
    # 密码
    password:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: *************************************************************************************************************************************************
        username: root
        password: 123456
        #connection-properties: config.decrypt=true;config.decrypt.key=${publicKey}
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30
# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api
