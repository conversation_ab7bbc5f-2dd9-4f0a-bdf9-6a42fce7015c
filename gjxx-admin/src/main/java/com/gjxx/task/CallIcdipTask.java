package com.gjxx.task;

import com.gjxx.common.core.redis.RedisCache;
import com.gjxx.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 调用智能教练交互平台定时任务调度
 */
@Slf4j
@Component("callIcdipTask")
public class CallIcdipTask {

    @Autowired
    private RedisCache redisCache;

    /**
     * 照片上报定时任务
     */
    public void photoReport() {
        log.info("照片上报定时任务开始执行："+ DateUtils.getTime());
    }
}
