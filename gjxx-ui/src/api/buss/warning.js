import request from '@/utils/request'

// 查询报警信息列表
export function listWarning(query) {
  return request({
    url: '/buss/warning/list',
    method: 'get',
    params: query
  })
}

// 查询报警信息详细
export function getWarning(id) {
  return request({
    url: '/buss/warning/' + id,
    method: 'get'
  })
}

// 新增报警信息
export function addWarning(data) {
  return request({
    url: '/buss/warning',
    method: 'post',
    data: data
  })
}

// 修改报警信息
export function updateWarning(data) {
  return request({
    url: '/buss/warning',
    method: 'put',
    data: data
  })
}

// 批量处理报警信息
export function handleBatch(id) {
  return request({
    url: '/buss/warning/handleBatch/' + id,
    method: 'get'
  })
}

// 暂不处理报警信息
export function handleNo(id) {
  return request({
    url: '/buss/warning/handleNo/' + id,
    method: 'get'
  })
}
