# JT808协议升级指南

## 概述

本次升级为 `JT808ProtocolUtils` 类添加了对 JT/T808-2019 版本协议的支持，同时保持对 JT/T808-2013 版本的完全向后兼容。

## 主要变更

### 1. 协议版本枚举

新增了 `ProtocolVersion` 枚举来标识不同的协议版本：

```java
public enum ProtocolVersion {
    V2013("2013", "JT/T808-2013"),
    V2019("2019", "JT/T808-2019");
}
```

### 2. 消息体属性生成

#### 2013版本（保持兼容）
```java
// 原有方法保持不变
int msgBodyProps = protocolUtils.generateMsgBodyProps(msgLen, encryptionType, isSubPackage, reserved);
```

#### 2019版本（新增）
```java
// 新增支持版本参数的方法
int msgBodyProps = protocolUtils.generateMsgBodyProps(
    msgLen, encryptionType, isSubPackage, versionFlag, 
    ProtocolVersion.V2019
);
```

### 3. 消息头生成

#### 2013版本（保持兼容）
```java
// 原有方法保持不变
byte[] header = protocolUtils.generateMsgHeader(phone, msgType, body, msgBodyProps, flowId);
```

#### 2019版本（新增）
```java
// 新增支持版本参数的方法
byte[] header = protocolUtils.generateMsgHeader(
    phone, msgType, body, msgBodyProps, flowId, 
    ProtocolVersion.V2019
);
```

## 协议版本差异

### JT/T808-2013 vs JT/T808-2019

| 字段 | 2013版本 | 2019版本 | 说明 |
|------|----------|----------|------|
| 消息ID | WORD(2字节) | WORD(2字节) | 无变化 |
| 消息体属性 | WORD(2字节) | WORD(2字节) | 版本标识位不同 |
| 协议版本号 | 无 | BYTE(1字节) | 2019版本新增，固定为0x01 |
| 终端手机号 | BCD[6] | BCD[10] | 2019版本扩展为10字节 |
| 消息流水号 | WORD(2字节) | WORD(2字节) | 无变化 |
| **消息头总长度** | **12字节** | **17字节** | 2019版本增加5字节 |

### 消息体属性位定义

#### 2013版本
- [0-9]：消息体长度
- [10-12]：加密类型
- [13]：是否有子包
- [14-15]：保留位

#### 2019版本
- [0-9]：消息体长度
- [10-12]：加密类型
- [13]：是否有子包
- [14-15]：版本标识位（固定为01）

## 使用指南

### 1. 自动版本检测

```java
// 从接收到的数据中自动检测协议版本
ProtocolVersion version = JT808ProtocolUtils.detectProtocolVersion(receivedData);

// 根据版本获取消息头长度
boolean hasSubPackage = ((msgBodyProps & 0x2000) >> 13) == 1;
int headerLength = JT808ProtocolUtils.getMsgHeaderLength(version, hasSubPackage);
```

### 2. 版本兼容的消息解析

```java
public PackageData parseMessage(byte[] data) {
    // 1. 检测协议版本
    ProtocolVersion version = JT808ProtocolUtils.detectProtocolVersion(data);
    
    // 2. 根据版本解析消息头
    if (version == ProtocolVersion.V2019) {
        return parseMessage2019(data);
    } else {
        return parseMessage2013(data);
    }
}
```

### 3. 发送消息时指定版本

```java
// 发送2019版本消息
public byte[] buildMessage2019(String phone, int msgType, byte[] msgBody, int flowId) {
    try {
        // 生成2019版本消息体属性
        int msgBodyProps = protocolUtils.generateMsgBodyProps(
            msgBody.length, 0, false, 0, ProtocolVersion.V2019
        );
        
        // 生成2019版本消息头
        byte[] header = protocolUtils.generateMsgHeader(
            phone, msgType, msgBody, msgBodyProps, flowId, ProtocolVersion.V2019
        );
        
        // 组装完整消息...
        return assembleMessage(header, msgBody);
    } catch (Exception e) {
        log.error("构建2019版本消息失败", e);
        return null;
    }
}
```

## 迁移建议

### 1. 渐进式迁移

1. **第一阶段**：保持现有代码不变，所有消息默认使用2013版本
2. **第二阶段**：在消息解析时添加版本检测逻辑
3. **第三阶段**：根据终端设备能力，选择性发送2019版本消息

### 2. 配置化版本选择

```java
// 建议在配置文件中设置默认协议版本
public class JT808Config {
    private ProtocolVersion defaultVersion = ProtocolVersion.V2013;
    private Map<String, ProtocolVersion> deviceVersionMap = new HashMap<>();
    
    public ProtocolVersion getVersionForDevice(String phone) {
        return deviceVersionMap.getOrDefault(phone, defaultVersion);
    }
}
```

### 3. 兼容性处理

```java
// 在MyDecoder中添加版本检测
public class MyDecoder extends ByteToMessageDecoder {
    
    private MsgHeader parseMsgHeaderFromBytes(byte[] data) {
        // 检测协议版本
        ProtocolVersion version = JT808ProtocolUtils.detectProtocolVersion(data);
        
        if (version == ProtocolVersion.V2019) {
            return parseMsgHeader2019(data);
        } else {
            return parseMsgHeader2013(data);
        }
    }
    
    private MsgHeader parseMsgHeader2019(byte[] data) {
        MsgHeader msgHeader = new MsgHeader();
        
        // 1. 消息ID
        msgHeader.setMsgId(this.parseIntFromBytes(data, 0, 2));
        
        // 2. 消息体属性
        int msgBodyProps = this.parseIntFromBytes(data, 2, 2);
        msgHeader.setMsgBodyPropsField(msgBodyProps);
        // ... 解析消息体属性各字段
        
        // 3. 协议版本号（2019版本新增）
        int protocolVersion = this.parseIntFromBytes(data, 4, 1);
        msgHeader.setProtocolVersion(protocolVersion);
        
        // 4. 终端手机号（2019版本为10字节）
        msgHeader.setTerminalPhone(this.parseBcdStringFromBytes(data, 5, 10));
        
        // 5. 消息流水号
        msgHeader.setFlowId(this.parseIntFromBytes(data, 15, 2));
        
        // 6. 消息包封装项（如果有子包）
        if (msgHeader.isHasSubPackage()) {
            msgHeader.setTotalSubPackage(this.parseIntFromBytes(data, 17, 2));
            msgHeader.setSubPackageSeq(this.parseIntFromBytes(data, 19, 2));
        }
        
        return msgHeader;
    }
}
```

## 注意事项

1. **向后兼容**：所有原有的API调用保持不变，默认使用2013版本
2. **版本检测**：建议在接收消息时自动检测版本，在发送消息时根据设备能力选择版本
3. **手机号处理**：2019版本的手机号字段扩展为10字节，需要注意填充和截取逻辑
4. **消息头长度**：2019版本的消息头比2013版本多5字节，解析时需要注意偏移量
5. **测试验证**：升级后需要充分测试与不同版本终端设备的兼容性

## 示例代码

完整的使用示例请参考 `JT808ProtocolExample.java` 文件。

## 相关文档

- JT/T808-2013 道路运输车辆卫星定位系统终端通讯协议及数据格式
- JT/T808-2019 道路运输车辆卫星定位系统终端通讯协议及数据格式