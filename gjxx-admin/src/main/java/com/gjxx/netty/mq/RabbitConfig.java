package com.gjxx.netty.mq;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitConfig {

    public final static String trajectory_queue = "gps.trajectory";//轨迹定位
    public final static String mediafile_queue = "gps.mediafile";//多媒体文件
    public final static String warning_queue = "gps.warning";//报警信息
    public final static String trajectoryExchange = "gps.trajectory.exchange";//交换机名称
    public final static String mediafileExchange = "gps.mediafile.exchange";//交换机名称
    public final static String warningExchange = "gps.warning.exchange";//交换机名称

    @Bean
    public Queue trajectoryQueue() {
        return new Queue(trajectory_queue);
    }
    @Bean
    public DirectExchange trajectoryExchange() {
        //定义direct交换机，参数分别为交换机名称，是否持久化，是否自动删除
        return new DirectExchange(trajectoryExchange);
    }
    @Bean
    public Binding bindingTrajectoryQueue(){
        //把队列和交换机进行一个绑定。这里的routingKey使用的是队列名
        return BindingBuilder.bind(trajectoryQueue()).to(trajectoryExchange()).withQueueName();
    }


    @Bean
    public Queue mediafileQueue() {
        return new Queue(mediafile_queue);
    }
    @Bean
    public DirectExchange mediafileExchange() {
        //定义direct交换机，参数分别为交换机名称，是否持久化，是否自动删除
        return new DirectExchange(mediafileExchange);
    }
    @Bean
    public Binding bindingMediafileQueue(){
        //把队列和交换机进行一个绑定。这里的routingKey使用的是队列名
        return BindingBuilder.bind(mediafileQueue()).to(mediafileExchange()).withQueueName();
    }

    @Bean
    public Queue warningQueue() {
        return new Queue(warning_queue);
    }
    @Bean
    public DirectExchange warningExchange() {
        //定义direct交换机，参数分别为交换机名称，是否持久化，是否自动删除
        return new DirectExchange(warningExchange);
    }
    @Bean
    public Binding bindingWarningQueue(){
        //把队列和交换机进行一个绑定。这里的routingKey使用的是队列名
        return BindingBuilder.bind(warningQueue()).to(warningExchange()).withQueueName();
    }

}
