package com.gjxx.buss.mapper;

import com.gjxx.common.mp.mapper.MpBaseMapper;
import java.util.List;
import java.util.Map;
import com.gjxx.buss.domain.TCarDev;
import org.apache.ibatis.annotations.Param;

/**
 * 车辆设备信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface TCarDevMapper extends MpBaseMapper<TCarDev> {


    /**
     * 查询车辆设备信息列表
     *
     * @param tCarDev 车辆设备信息
     * @return 车辆设备信息集合
     */
    List<TCarDev> selectTCarDevList(TCarDev tCarDev);
    /**
     * 根据车队ID递归查询车辆设备信息
     * @param carTeamId
     * @return
     */
    List<TCarDev> listChildByCarTeamId(@Param("carTeamId") Long carTeamId);
    /**
     * 根据车队ID递归查询车辆设备Sim卡号
     * @param carTeamId
     * @return
     */
    List<String> listChildSimNoByCarTeamId(@Param("carTeamId") Long carTeamId);
}
