package ${packageName}.service;

import com.gjxx.common.mp.service.MpService;
import java.util.List;
import java.util.Map;
import ${packageName}.domain.${ClassName};

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service extends MpService<${ClassName}> {

    /**
     * 查询${functionName}列表
     *
     * @param ${className} ${functionName}
     * @return ${functionName}集合
     */
    List<${ClassName}> select${ClassName}List(${ClassName} ${className});

}
