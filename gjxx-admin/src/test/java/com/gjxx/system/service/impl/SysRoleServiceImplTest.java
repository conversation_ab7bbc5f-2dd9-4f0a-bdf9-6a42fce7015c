package com.gjxx.system.service.impl;

import com.gjxx.BaseServiceTest;
import com.gjxx.common.core.domain.entity.SysRole;
import com.gjxx.system.service.ISysRoleService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SysRoleServiceImplTest extends BaseServiceTest {

    @Autowired
    private ISysRoleService roleService;

    @Test
    void getById() {
        SysRole byId = this.roleService.getById(1L);
        System.out.println("byId = " + byId);
    }

    @Test
    void selectRoleById() {
        SysRole byId = this.roleService.selectRoleById(1L);
        System.out.println("byId = " + byId);
    }
}
