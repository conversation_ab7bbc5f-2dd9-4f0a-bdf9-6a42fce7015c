package com.gjxx.buss.service.impl;

import com.gjxx.common.mp.service.impl.MpServiceImpl;
import java.util.List;
import org.springframework.stereotype.Service;
import com.gjxx.buss.mapper.TMediaFileMapper;
import com.gjxx.buss.domain.TMediaFile;
import com.gjxx.buss.service.ITMediaFileService;

/**
 * 多媒体文件信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class TMediaFileServiceImpl extends MpServiceImpl<TMediaFileMapper, TMediaFile> implements ITMediaFileService {

    /**
     * 查询多媒体文件信息列表
     *
     * @param tMediaFile 多媒体文件信息
     * @return 多媒体文件信息
     */
    @Override
    public List<TMediaFile> selectTMediaFileList(TMediaFile tMediaFile) {
        return baseMapper.selectTMediaFileList(tMediaFile);
    }

}
