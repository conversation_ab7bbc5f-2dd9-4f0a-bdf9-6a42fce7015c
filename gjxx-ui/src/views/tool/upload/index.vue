<template>
  <div class="app-container">
    <el-row :gutter="15">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
        <el-col :span="12">
          <el-form-item label="图片组件" prop="field101" required>
            <image-upload v-model="formData.field101" :limit="2" :is-show-tip="false"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文件组件" prop="field102" required>
            <file-upload v-model="formData.field102" :limit="2" :is-show-tip="false"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="富文本组件" prop="field103" required>
            <editor v-model="formData.field103" :height="300"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item size="large">
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>
<script>
  export default {
    components: {},
    props: [],
    data() {
      return {
        formData: {
          field101: null,
          field102: null,
          field103: null,
        },
        rules: {},
      }
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {},
    methods: {
      submitForm() {
        this.$refs['elForm'].validate(valid => {
          if (!valid) return
          // TODO 提交表单
          alert(this.formData.field101);
          console.log(this.formData);
        })
      },
      resetForm() {
        this.$refs['elForm'].resetFields()
      },
      field101BeforeUpload(file) {
        let isRightSize = file.size / 1024 / 1024 < 2
        if (!isRightSize) {
          this.$message.error('文件大小超过 2MB')
        }
        return isRightSize
      },
    }
  }

</script>
<style>
  /*.el-form-item__label {*/
  /*  width: 25% !important;*/
  /*}*/

  .el-upload__tip {
    line-height: 1.2;
  }

</style>
