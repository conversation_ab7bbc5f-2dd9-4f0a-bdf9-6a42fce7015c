# JT808协议兼容性升级指南

## 概述

本次升级主要解决了 `doEscape4Receive` 方法只能正确解析 JT/T808-2013 协议的问题，现在已经完全兼容 JT/T808-2019 协议。

## 主要变更

### 1. JT808ProtocolUtils.java 变更

#### 新增方法
- `doEscape4ReceiveCompatible(byte[] bs)`: 新的兼容性转义方法，自动处理完整的JT808消息包
- 保留原有的 `doEscape4Receive(byte[] bs, int start, int end)` 方法，确保向后兼容

#### 已有的协议版本支持方法
- `detectProtocolVersion(byte[] data)`: 从原始数据中检测协议版本
- `parseProtocolVersion(int msgBodyProps)`: 从消息体属性中解析协议版本
- `getMsgHeaderLength(ProtocolVersion version, boolean hasSubPackage)`: 获取消息头长度
- `getPhoneStartIndex(ProtocolVersion version)`: 获取手机号起始位置
- `getPhoneLength(ProtocolVersion version)`: 获取手机号字段长度

### 2. MyDecoder.java 变更

#### 修改的方法
- `bytes2PackageData(byte[] data)`: 增加了协议版本检测逻辑
- `parseMsgHeaderFromBytes(byte[] data, ProtocolVersion protocolVersion)`: 新增重载方法，支持协议版本参数
- 保留原有的 `parseMsgHeaderFromBytes(byte[] data)` 方法，默认使用2013协议，确保向后兼容

## 协议版本差异

### JT/T808-2013 vs JT/T808-2019

| 字段 | JT/T808-2013 | JT/T808-2019 |
|------|-------------|-------------|
| 消息ID | 2字节 | 2字节 |
| 消息体属性 | 2字节 | 2字节 |
| 协议版本号 | 无 | 1字节 (固定0x01) |
| 终端手机号 | BCD[6] | BCD[10] |
| 消息流水号 | 2字节 | 2字节 |
| **消息头长度** | **12字节** | **17字节** |
| **版本标识位** | **[14-15]位为00** | **[14-15]位为01** |

### 消息体属性中的版本标识

- **JT/T808-2013**: 消息体属性的第14-15位为 `00`
- **JT/T808-2019**: 消息体属性的第14-15位为 `01`

## 使用指南

### 1. 推荐使用新的兼容方法

```java
// 推荐：使用新的兼容性方法
try {
    byte[] originalData = ...; // 完整的JT808消息包（包含0x7E起始和结束符）
    byte[] escapedData = JT808ProtocolUtils.doEscape4ReceiveCompatible(originalData);
    // 后续处理...
} catch (Exception e) {
    log.error("转义处理失败: {}", e.getMessage(), e);
}
```

### 2. 协议版本检测

```java
// 检测协议版本
JT808ProtocolUtils.ProtocolVersion version = JT808ProtocolUtils.detectProtocolVersion(data);
if (version == JT808ProtocolUtils.ProtocolVersion.V2019) {
    // 处理2019协议
} else {
    // 处理2013协议
}
```

### 3. 消息头解析

```java
// 自动检测版本并解析
MyDecoder decoder = new MyDecoder();
PackageData packageData = decoder.bytes2PackageData(escapedData);
MsgHeader msgHeader = packageData.getMsgHeader();
```

## 迁移建议

### 1. 无需修改现有代码

现有使用 `doEscape4Receive(byte[] bs, int start, int end)` 的代码无需修改，该方法仍然可以正常工作。

### 2. 新项目推荐使用

对于新的项目或新的功能模块，推荐使用 `doEscape4ReceiveCompatible(byte[] bs)` 方法，它会自动处理协议版本差异。

### 3. 逐步迁移

可以逐步将现有代码迁移到新的兼容方法：

```java
// 旧代码
byte[] escapedData = JT808ProtocolUtils.doEscape4Receive(data, 1, data.length - 1);

// 新代码
byte[] escapedData = JT808ProtocolUtils.doEscape4ReceiveCompatible(data);
```

## 兼容性保证

### 1. 向后兼容

- 所有原有的API方法都保持不变
- 原有的调用方式继续有效
- 默认行为保持与2013协议一致

### 2. 自动检测

- 新的方法会自动检测协议版本
- 根据检测结果选择合适的解析逻辑
- 无需手动指定协议版本

## 测试验证

### 1. 运行测试示例

```bash
# 运行兼容性测试示例
java com.gjxx.netty.codec.JT808ProtocolCompatibilityExample
```

### 2. 测试内容

- JT/T808-2013协议消息解析
- JT/T808-2019协议消息解析
- 协议版本自动检测
- 转义处理验证

## 注意事项

### 1. 数据格式要求

`doEscape4ReceiveCompatible` 方法要求输入的数据必须是完整的JT808消息包，包含起始符0x7E和结束符0x7E。

### 2. 异常处理

```java
try {
    byte[] result = JT808ProtocolUtils.doEscape4ReceiveCompatible(data);
} catch (IllegalArgumentException e) {
    // 处理数据格式错误
} catch (Exception e) {
    // 处理其他异常
}
```

### 3. 性能考虑

新的兼容方法会进行协议版本检测，相比原方法有轻微的性能开销，但在实际应用中影响可忽略。

## 常见问题

### Q1: 如何判断当前使用的是哪个协议版本？

A1: 使用 `JT808ProtocolUtils.detectProtocolVersion(data)` 方法可以自动检测协议版本。

### Q2: 原有代码是否需要修改？

A2: 不需要。所有原有API保持向后兼容，原有代码可以继续正常工作。

### Q3: 新方法和旧方法的主要区别是什么？

A3: 新方法 `doEscape4ReceiveCompatible` 会自动检测协议版本并处理完整的消息包，而旧方法需要手动指定起始和结束位置。

### Q4: 如何确保解析的准确性？

A4: 建议使用提供的测试示例进行验证，确保在你的环境中能够正确解析两种协议版本的消息。

## 相关文档

- [JT808协议升级指南](./JT808_UPGRADE_GUIDE.md)
- [JT808协议示例代码](./src/main/java/com/gjxx/netty/codec/JT808ProtocolExample.java)
- [JT808兼容性测试示例](./src/main/java/com/gjxx/netty/codec/JT808ProtocolCompatibilityExample.java)