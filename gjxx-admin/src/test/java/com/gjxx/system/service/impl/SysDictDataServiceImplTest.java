package com.gjxx.system.service.impl;

import com.gjxx.BaseServiceTest;
import com.gjxx.common.core.domain.entity.SysDictData;
import com.gjxx.system.service.ISysDictDataService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SysDictDataServiceImplTest extends BaseServiceTest {

    @Autowired
    private ISysDictDataService dictDataService;

    @Test
    void getById() {
        SysDictData byId = this.dictDataService.getById(1L);
        System.out.println("byId = " + byId);
    }

    @Test
    void selectDictDataById() {
        SysDictData byId = this.dictDataService.selectDictDataById(1L);
        System.out.println("byId = " + byId);
    }
}
