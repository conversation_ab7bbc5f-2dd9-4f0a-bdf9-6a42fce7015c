<template>
  <div class="videoWindow main">
    <div id="video">
    </div>
  </div>
</template>

<script>
    import $ from "jquery";

    export default {
      data() {
          return {
          };
      },
      created() {
        $("#video").empty();
      },
      methods: {
      }
    };
</script>
<style lang="scss" scoped>
  .videoWindow {
    background: #fff;
    padding: 10px !important;
    border-radius: 5px;
    z-index: 100;
    min-width: 400px !important;
    min-height: 337px !important;
  }
</style>
