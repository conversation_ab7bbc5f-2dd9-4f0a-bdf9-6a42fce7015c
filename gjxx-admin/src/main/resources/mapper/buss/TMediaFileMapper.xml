<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gjxx.buss.mapper.TMediaFileMapper">


    <select id="selectTMediaFileList" parameterType="com.gjxx.buss.domain.TMediaFile" resultType="com.gjxx.buss.domain.TMediaFile">
        select a.*,b.vehicle_id from t_media_file a left join t_car_dev b on a.sim_no = b.sim_no
        <where>
            <if test="simNo != null  and simNo != ''">
                and a.sim_no = #{simNo}
            </if>
            <if test="fileType != null  and fileType != ''">
                and a.file_type = #{fileType}
            </if>
            <if test="formatCode != null  and formatCode != ''">
                and a.format_code = #{formatCode}
            </if>
            <if test="eventCode != null  and eventCode != ''">
                and a.event_code = #{eventCode}
            </if>
            <if test="filePath != null  and filePath != ''">
                and a.file_path = #{filePath}
            </if>
            <if test="vedioCh != null  and vedioCh != ''">
                and a.vedio_ch = #{vedioCh}
            </if>
            <if test="selectCarDevId != null  and selectCarDevId != ''">
                and b.id = #{selectCarDevId}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(a.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(a.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
    </select>
</mapper>
