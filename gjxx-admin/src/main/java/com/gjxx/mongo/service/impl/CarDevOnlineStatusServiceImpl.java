package com.gjxx.mongo.service.impl;

import com.gjxx.mongo.dao.CarDevOnlineStatusRepository;
import com.gjxx.mongo.entity.CarDevOnlineStatus;
import com.gjxx.mongo.service.CarDevOnlineStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CarDevOnlineStatusServiceImpl implements CarDevOnlineStatusService {
    //注入dao
    @Autowired
    private CarDevOnlineStatusRepository carDevOnlineStatusRepository;
    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public CarDevOnlineStatus findCarDevOnlineStatusById(String id) {
        return carDevOnlineStatusRepository.findCarDevOnlineStatusById(id);
    }

    @Override
    public void save(CarDevOnlineStatus carDevOnlineStatus) {
        carDevOnlineStatusRepository.save(carDevOnlineStatus);
    }

    @Override
    public void delete(String id) {
        carDevOnlineStatusRepository.deleteById(id);
    }

    @Override
    public List<CarDevOnlineStatus> findAll() {
        return (List<CarDevOnlineStatus>) carDevOnlineStatusRepository.findAll();
    }

    @Override
    public List<CarDevOnlineStatus> findAllByIds(List<String> ids) {
        return (List<CarDevOnlineStatus>) carDevOnlineStatusRepository.findAllById(ids);
    }

    @Override
    public List<CarDevOnlineStatus> findAllByCarTeamIds(List<Long> tCarTeamIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where("carTeamId").in(tCarTeamIds));
        return mongoTemplate.find(query, CarDevOnlineStatus.class);
    }
}


