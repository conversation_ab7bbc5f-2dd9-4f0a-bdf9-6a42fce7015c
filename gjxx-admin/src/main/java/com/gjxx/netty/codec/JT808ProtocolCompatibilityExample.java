package com.gjxx.netty.codec;

import com.gjxx.netty.MyDecoder;
import com.gjxx.common.tcp.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JT808协议兼容性示例
 * 演示如何使用修改后的doEscape4Receive方法处理JT/T808-2013和JT/T808-2019协议
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */
public class JT808ProtocolCompatibilityExample {
    
    private static final Logger log = LoggerFactory.getLogger(JT808ProtocolCompatibilityExample.class);
    
    public static void main(String[] args) {
        JT808ProtocolCompatibilityExample example = new JT808ProtocolCompatibilityExample();
        
        // 测试JT/T808-2013协议
        example.testJT808_2013Protocol();
        
        // 测试JT/T808-2019协议
        example.testJT808_2019Protocol();
        
        // 测试协议版本检测
        example.testProtocolVersionDetection();
    }
    
    /**
     * 测试JT/T808-2013协议解析
     */
    public void testJT808_2013Protocol() {
        log.info("=== 测试JT/T808-2013协议解析 ===");
        
        // 模拟JT/T808-2013协议的消息数据（心跳包示例）
        // 消息ID: 0x0002(心跳), 消息体属性: 0x0000, 终端手机号: 013912345678(BCD[6]), 流水号: 0x0001
        byte[] jt808_2013_data = {
            0x7E, // 起始符
            0x00, 0x02, // 消息ID: 心跳
            0x00, 0x00, // 消息体属性: 无加密、无子包、消息体长度0、版本位00(2013)
            0x01, 0x39, 0x12, 0x34, 0x56, 0x78, // 终端手机号 BCD[6]
            0x00, 0x01, // 消息流水号
            0x01, // 校验码(示例)
            0x7E  // 结束符
        };
        
        try {
            // 使用兼容的转义方法
            byte[] escapedData = JT808ProtocolUtils.doEscape4ReceiveCompatible(jt808_2013_data);
            
            // 检测协议版本
            JT808ProtocolUtils.ProtocolVersion version = JT808ProtocolUtils.detectProtocolVersion(escapedData);
            log.info("检测到协议版本: {}", version);
            
            // 解析消息头
            MyDecoder decoder = new MyDecoder();
            PackageData packageData = decoder.bytes2PackageData(escapedData);
            MsgHeader msgHeader = packageData.getMsgHeader();
            
            log.info("消息ID: 0x{}", Integer.toHexString(msgHeader.getMsgId()).toUpperCase());
            log.info("终端手机号: {}", msgHeader.getTerminalPhone());
            log.info("消息流水号: {}", msgHeader.getFlowId());
            log.info("消息体长度: {}", msgHeader.getMsgBodyLength());
            
        } catch (Exception e) {
            log.error("JT/T808-2013协议解析失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试JT/T808-2019协议解析
     */
    public void testJT808_2019Protocol() {
        log.info("=== 测试JT/T808-2019协议解析 ===");
        
        // 模拟JT/T808-2019协议的消息数据（心跳包示例）
        // 消息ID: 0x0002(心跳), 消息体属性: 0x4000(版本位01), 协议版本: 0x01, 终端手机号: 013912345678(BCD[10]), 流水号: 0x0001
        byte[] jt808_2019_data = {
            0x7E, // 起始符
            0x00, 0x02, // 消息ID: 心跳
            0x40, 0x00, // 消息体属性: 无加密、无子包、消息体长度0、版本位01(2019)
            0x01, // 协议版本号
            0x01, 0x39, 0x12, 0x34, 0x56, 0x78, 0x00, 0x00, 0x00, 0x00, // 终端手机号 BCD[10]
            0x00, 0x01, // 消息流水号
            0x01, // 校验码(示例)
            0x7E  // 结束符
        };
        
        try {
            // 使用兼容的转义方法
            byte[] escapedData = JT808ProtocolUtils.doEscape4ReceiveCompatible(jt808_2019_data);
            
            // 检测协议版本
            JT808ProtocolUtils.ProtocolVersion version = JT808ProtocolUtils.detectProtocolVersion(escapedData);
            log.info("检测到协议版本: {}", version);
            
            // 解析消息头
            MyDecoder decoder = new MyDecoder();
            PackageData packageData = decoder.bytes2PackageData(escapedData);
            MsgHeader msgHeader = packageData.getMsgHeader();
            
            log.info("消息ID: 0x{}", Integer.toHexString(msgHeader.getMsgId()).toUpperCase());
            log.info("终端手机号: {}", msgHeader.getTerminalPhone());
            log.info("消息流水号: {}", msgHeader.getFlowId());
            log.info("消息体长度: {}", msgHeader.getMsgBodyLength());
            
        } catch (Exception e) {
            log.error("JT/T808-2019协议解析失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试协议版本检测功能
     */
    public void testProtocolVersionDetection() {
        log.info("=== 测试协议版本检测功能 ===");
        
        // 测试2013版本检测
        byte[] data2013 = {0x00, 0x02, 0x00, 0x00}; // 版本位00
        JT808ProtocolUtils.ProtocolVersion version2013 = JT808ProtocolUtils.detectProtocolVersion(data2013);
        log.info("2013版本检测结果: {}", version2013);
        
        // 测试2019版本检测
        byte[] data2019 = {0x00, 0x02, 0x40, 0x00}; // 版本位01
        JT808ProtocolUtils.ProtocolVersion version2019 = JT808ProtocolUtils.detectProtocolVersion(data2019);
        log.info("2019版本检测结果: {}", version2019);
        
        // 测试消息头长度计算
        int headerLength2013 = JT808ProtocolUtils.getMsgHeaderLength(JT808ProtocolUtils.ProtocolVersion.V2013, false);
        int headerLength2019 = JT808ProtocolUtils.getMsgHeaderLength(JT808ProtocolUtils.ProtocolVersion.V2019, false);
        log.info("2013版本消息头长度: {} 字节", headerLength2013);
        log.info("2019版本消息头长度: {} 字节", headerLength2019);
        
        // 测试带子包的消息头长度
        int headerLengthWithSub2013 = JT808ProtocolUtils.getMsgHeaderLength(JT808ProtocolUtils.ProtocolVersion.V2013, true);
        int headerLengthWithSub2019 = JT808ProtocolUtils.getMsgHeaderLength(JT808ProtocolUtils.ProtocolVersion.V2019, true);
        log.info("2013版本带子包消息头长度: {} 字节", headerLengthWithSub2013);
        log.info("2019版本带子包消息头长度: {} 字节", headerLengthWithSub2019);
    }
    
    /**
     * 演示转义处理的差异
     */
    public void demonstrateEscapeProcessing() {
        log.info("=== 演示转义处理 ===");
        
        // 包含需要转义字符的数据
        byte[] dataWithEscape = {
            0x7E, // 起始符
            0x00, 0x02, // 消息ID
            0x00, 0x02, // 消息体属性
            0x7D, 0x02, // 需要转义的0x7E -> 0x7D 0x02
            0x7D, 0x01, // 需要转义的0x7D -> 0x7D 0x01
            0x01, // 校验码
            0x7E  // 结束符
        };
        
        try {
            byte[] escapedData = JT808ProtocolUtils.doEscape4ReceiveCompatible(dataWithEscape);
            log.info("转义前数据长度: {}", dataWithEscape.length);
            log.info("转义后数据长度: {}", escapedData.length);
            
            // 打印转义前后的数据对比
            StringBuilder beforeEscape = new StringBuilder();
            StringBuilder afterEscape = new StringBuilder();
            
            for (byte b : dataWithEscape) {
                beforeEscape.append(String.format("%02X ", b & 0xFF));
            }
            
            for (byte b : escapedData) {
                afterEscape.append(String.format("%02X ", b & 0xFF));
            }
            
            log.info("转义前: {}", beforeEscape.toString());
            log.info("转义后: {}", afterEscape.toString());
            
        } catch (Exception e) {
            log.error("转义处理失败: {}", e.getMessage(), e);
        }
    }
}