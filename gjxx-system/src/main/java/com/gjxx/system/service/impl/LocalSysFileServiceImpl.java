package com.gjxx.system.service.impl;

import com.gjxx.common.config.GjxxConfig;
import com.gjxx.common.utils.ServletUtils;
import com.gjxx.common.utils.file.FileUploadUtils;
import com.gjxx.common.utils.file.FileUtils;
import com.gjxx.common.utils.file.MimeTypeUtils;
import com.gjxx.system.domain.vo.FileVo;
import com.gjxx.system.service.ISysFileService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * 本地文件存储
 *
 * <AUTHOR>
 */
//@Service
public class LocalSysFileServiceImpl {
//public class LocalSysFileServiceImpl implements ISysFileService {
//
//    /**
//     * 本地文件上传接口
//     *
//     * @param file 上传的文件
//     * @return 访问地址
//     * @throws Exception
//     */
//    @Override
//    public String uploadFile(MultipartFile file,String stuNum) throws Exception {
//        return FileUploadUtils.upload(GjxxConfig.getAvatarPath(), file, MimeTypeUtils.IMAGE_EXTENSION);
//    }
//
//    @Override
//    public FileVo uploadFileNew(MultipartFile file,String stuNum) throws Exception {
//        HttpServletRequest request = ServletUtils.getRequest();
//        int serverPort = request.getServerPort();
//        String localName = request.getLocalName();
//        String scheme = request.getScheme();
//        String contextPath = request.getContextPath();
//
//        FileVo fileVo = new FileVo();
//        String fileName = this.uploadFile(file,stuNum);
//        fileVo.setFileName(fileName);
//        String domain = scheme + "://" + localName + ":" + serverPort + contextPath;
//        fileVo.setUrl(domain + fileName);
//        fileVo.setDomain(domain);
//        fileVo.setNewFileName(FileUtils.getName(fileName));
//        fileVo.setOriginalFilename(file.getOriginalFilename());
//        return fileVo;
//    }
//
//    @Override
//    public String uploadFile(byte[] fileBytes, String fileNameHead) throws Exception {
//        return null;
//    }
}
