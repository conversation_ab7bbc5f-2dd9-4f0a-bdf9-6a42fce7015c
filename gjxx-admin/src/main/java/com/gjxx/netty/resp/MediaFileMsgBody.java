package com.gjxx.netty.resp;

import java.util.Arrays;
import java.util.List;

public class MediaFileMsgBody {

	// byte[0-3] 多媒体文件ID
	private int fileId;
	// byte[4] 重传包总数
	private byte[] tempCount;
	// byte[5] 重传包ID列表
	private List<byte[]> tmpList;

	public int getFileId() {
		return fileId;
	}

	public void setFileId(int fileId) {
		this.fileId = fileId;
	}

	public byte[] getTempCount() {
		return tempCount;
	}

	public void setTempCount(byte[] tempCount) {
		this.tempCount = tempCount;
	}

	public List<byte[]> getTmpList() {
		return tmpList;
	}

	public void setTmpList(List<byte[]> tmpList) {
		this.tmpList = tmpList;
	}

	@Override
	public String toString() {
		return "MediaFileMsgBody{" +
				"fileId=" + fileId +
				", tempCount=" + Arrays.toString(tempCount) +
				", tmpList=" + tmpList +
				'}';
	}
}
