import request from '@/utils/request'
import flv from 'flv.js';

// 查询车辆设备树结构
export function getCarDevTree(query) {
  return request({
    url: '/buss/carDev/getCarDevTree',
    method: 'get',
    params: query
  })
}
// 根据车辆标识查询车辆树
export function getCarDevTreeByVehicleId(query) {
  return request({
    url: '/buss/carDev/getCarDevTreeByVehicleId',
    method: 'get',
    params: query
  })
}

// 车辆设备信息和视频播放
export function carDevAndVedioPlay(query) {
  return request({
    url: '/buss/carDev/carDevAndVedioPlay',
    method: 'get',
    params: query
  })
}

/**
 * 创建一个FLV播放器，参数如下：
 * container : 视频容器元素
 * muted     : 是否静音
 * url       : HTTP-FLV地址
 */
export function FLVPlayer(opts){
  var videoElement = document.createElement('VIDEO');
  videoElement.autoplay = true;
  videoElement.controls = false;
  videoElement.muted = false;
  videoElement.style.width = '100%';
  videoElement.style.height = '100%';
  opts.container.append(videoElement);
  this.container = opts.container;
  this.videoElement = videoElement;
  this.httpFlvURL = opts.url;
  this.mediaInfo = null;
  this.play = null;
  this.onPlayEvtListener = null;
  this.onPauseEvtListener = null;
  this.onStopEvtListener = null;
  this.autoFastForward = opts.autoFastForward;
  this.autoFastForwardInterval = null;

  this.play = function(){
    if (this.player) return;
    var self = this;
    self.player = new flv.createPlayer({
      type                            : 'flv',
      url                             : self.httpFlvURL,
      isLive                          : true,
      enableWorker                    : true,
      enableStashBuffer               : true,
      autoCleanupSourceBuffer         : true,
      autoCleanupMaxBackwardDuration  : 5,
      autoCleanupMinBackwardDuration  : 1
    });
    self.player.on('media_info', function(){
      self.mediaInfo = self.player.mediaInfo;
    });
    self.player.on('statistics_info', function(){
      // console.log("arguments==="+arguments);
    });
    var autoPlayTimer = null;
    self.videoElement.addEventListener('player', function(e){
      if (autoPlayTimer) clearInterval(autoPlayTimer);
      if (self.onPlayEvtListener) self.onPlayEvtListener(self, e);
    });
    self.videoElement.addEventListener('dblclick', function(){
      if (self.videoElement.requestFullscreen) self.videoElement.requestFullscreen();
    });
    autoPlayTimer = setInterval(function(){
      try { self.player.play(); } catch(e) { clearInterval(autoPlayTimer); };
    });
    self.player.attachMediaElement(self.videoElement);
    self.player.load();
    self.player.play();
    if (this.autoFastForward) this.autoFastForwardInterval = setInterval(function(){
      if (self.videoElement.buffered.length > 0 && self.videoElement.buffered.end(0) - self.videoElement.currentTime > 2){
        console.log("self==="+self.videoElement.buffered.end(0) + "-" + self.videoElement.currentTime);
        self.videoElement.currentTime = self.videoElement.buffered.end(0) - 1;
      }
    }, 1000);
  };
  this.fullscreen = function(){
    if (this.videoElement && this.videoElement.requestFullscreen)
      this.videoElement.requestFullscreen();
  };
  this.onPlay = function(fn){
    this.onPlayEvtListener = fn;
  };
  this.destroy = function(){
    this.player.destroy();
    clearInterval(this.autoFastForwardInterval);
  }
}


