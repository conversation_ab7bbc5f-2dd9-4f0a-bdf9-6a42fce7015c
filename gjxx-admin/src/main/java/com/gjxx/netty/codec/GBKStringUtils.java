package com.gjxx.netty.codec;

/**
 * @ Author     ：maogf
 * @ Date       ：2018/6/8 0008 17:26
 * @ Description：${description}
 */
public class GBKStringUtils {
	public static void main(String[] args) throws Exception {
		System.out.println(Chinese2GBK("鄂A90000"));
		System.out.println(Chinese2GBK("ZD00001"));
		System.out.println( Integer.parseInt("0025",16));
		System.out.println(GBK2Chinese("002301860070048600000025003445304438373139424441343232383444394542394137323042393634374245B4"));
		System.out.println(GBK2Chinese("3445304438373139424441343232383444394542394137323042393634374245"));
		String msgBody = "7E 81 00 00 23 01 86 00 70 04 86 00 00 00 25 00 34 45 30 44 38 37 31 39 42 44 41 34 32 32 38 34 44 39 45 42 39 41 37 32 30 42 39 36 34 37 42 45 B4 7E";
	}

	//中文转换成GBK码(16进制字符串)，每个汉字2个字节
	public static String Chinese2GBK(String chineseStr)throws Exception {
		StringBuffer GBKStr = new StringBuffer();
		byte[] GBKDecode = chineseStr.getBytes("gbk");
		for (byte b : GBKDecode)
			GBKStr.append(Integer.toHexString(b&0xFF));
		return GBKStr.toString();
	}

	//16进制GBK字符串转换成中文
	public static String GBK2Chinese(String GBKStr)throws Exception{
		byte[] b = HexString2Bytes(GBKStr);
		String chineseStr = new String(b, "gbk");//输入参数为字节数组
		return chineseStr;
	}

	//把16进制字符串转换成字节数组
	public static byte[] HexString2Bytes(String hexStr) {
		byte[] b = new byte[hexStr.length() / 2];
		for (int i = 0; i < b.length; i++)
			b[i]=(byte) Integer.parseInt(hexStr.substring(2*i,2*i+2),16);
		return b;
	}
}
