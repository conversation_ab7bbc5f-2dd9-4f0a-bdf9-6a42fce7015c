package com.gjxx.system.service.impl;

import com.gjxx.BaseServiceTest;
import com.gjxx.common.core.domain.entity.SysDept;
import com.gjxx.system.service.ISysDeptService;
//import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SysDeptServiceImplTest extends BaseServiceTest {

    @Autowired
    private ISysDeptService deptService;

    @Test
    void getById() {
        SysDept byId = deptService.getById(1L);
        System.out.println("byId = " + byId);
    }

    @Test
    void selectDeptById() {
        SysDept byId = deptService.selectDeptById(1L);
        System.out.println("byId = " + byId);
    }
}
