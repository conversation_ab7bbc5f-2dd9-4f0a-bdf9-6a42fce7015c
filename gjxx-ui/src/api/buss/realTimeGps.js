import request from '@/utils/request'

// 获取不带通道的车辆设备树列表
export function getCarDevTreeNoCHn(query) {
  return request({
    url: '/buss/carDev/getCarDevTreeNoCHn',
    method: 'get',
    params: query
  })
}
// 根据车辆标识查询不带通道的车辆树
export function getCarDevTreeNoCHnByVehicleId(query) {
  return request({
    url: '/buss/carDev/getCarDevTreeNoCHnByVehicleId',
    method: 'get',
    params: query
  })
}

// 发送车辆实时位置查询命令
export function carDevLocationQuery(query) {
  return request({
    url: '/buss/carDev/carDevLocationQuery',
    method: 'get',
    params: query
  })
}

// 查询车辆实时位置
export function locationQuery(query) {
  return request({
    url: '/buss/msgUpSelect/locationQuery',
    method: 'get',
    params: query
  })
}

