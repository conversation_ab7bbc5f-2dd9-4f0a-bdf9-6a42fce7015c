package com.gjxx.buss.domain.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 基础参数DTO
 */
@ApiModel("基础参数DTO")
@Data
public class BaseDTO implements Serializable {

    private static final long serialVersionUID = -6327863245437894287L;
    /**
     * sign 签名
     */
    @ApiModelProperty(value = "sign签名", required = true)
//    @NotBlank(message = "sign签名不能为空")
    private String sign;
}
